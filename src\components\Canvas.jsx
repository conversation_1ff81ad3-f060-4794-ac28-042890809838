import React, { forwardRef, useState, useRef, useEffect } from 'react';
import Draggable from 'react-draggable';
import { RotateCw } from 'lucide-react';
import ProductDetails from './ProductDetails';
import ProductHoverPreview from './ProductHoverPreview';
import ConnectionLine from './ConnectionLine';
import SelectionOverlay from './SelectionOverlay';
import ProductPNGRenderer from './ProductPNGRenderer';
import { useLanguage } from '../contexts/LanguageContext';
import { useSettings } from '../contexts/SettingsContext';
import { useDrag } from '../contexts/DragContext';
import { exportSelectedArea } from '../utils/exportUtils';

const Canvas = forwardRef(({
  products,
  connections,
  selectedProducts,
  isConnectMode,
  onProductSelect,
  onProductMove,
  onProductRemove,
  onProductRotate,
  onProductDrop,
  isSelectionMode,
  onSelectionConfirm,
  onSelectionCancel,
  onSelectionClear
}, ref) => {
  const { t } = useLanguage();
  const { settings } = useSettings();
  const { isDraggingFromSidebar, startDrag, endDrag } = useDrag();
  const [hoveredProduct, setHoveredProduct] = useState(null);
  const [hoverPosition, setHoverPosition] = useState({ x: 0, y: 0 });
  const [showEnhancedPreview, setShowEnhancedPreview] = useState(false);
  const [enhancedPreviewPosition, setEnhancedPreviewPosition] = useState({ x: 0, y: 0 });
  const [draggingItems, setDraggingItems] = useState(new Set());
  const canvasRef = useRef(null);
  const hoverTimeoutRef = useRef(null);

  // Handle selection confirmation
  const handleSelectionConfirm = async (selectionRect) => {
    if (canvasRef.current && selectionRect) {
      await exportSelectedArea(canvasRef.current, selectionRect, products);
      onSelectionConfirm(selectionRect);
    }
  };

  // Clean up hover state when products change (e.g., when a product is removed)
  useEffect(() => {
    if (hoveredProduct && !products.find(p => p.id === hoveredProduct.id)) {
      setHoveredProduct(null);
      setShowEnhancedPreview(false);
    }
  }, [products, hoveredProduct]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (hoverTimeoutRef.current) {
        clearTimeout(hoverTimeoutRef.current);
      }
    };
  }, []);

  const handleDrop = (e) => {
    e.preventDefault();
    const rect = canvasRef.current.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    try {
      const productData = JSON.parse(e.dataTransfer.getData('application/json'));
      onProductDrop(productData, { x, y });

      // End drag operation after successful drop
      endDrag();
    } catch (error) {
      console.error('Error parsing dropped data:', error);
      // End drag operation even if drop fails
      endDrag();
    }
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'copy';
  };

  const handleProductClick = (productId, e) => {
    e.stopPropagation();
    if (isConnectMode) {
      onProductSelect(productId);
    }
  };

  // Intersection-based dropdown positioning function with canvas boundary detection
  const calculateDropdownPosition = (mouseX, mouseY, rect, canvasRect, viewportWidth, viewportHeight) => {
    // Optimized dropdown dimensions - now dynamic to accommodate longer product names
    const dropdownWidth = 350;
    const dropdownHeight = 400; // Increased default height to accommodate longer product names
    const margin = 15; // minimum margin from canvas edges

    // Product node center for arrow positioning
    const productCenterX = rect.left + 50; // 50px from left edge of 100px wide node
    const productCenterY = rect.top + 35; // 35px from top edge of 70px high node

    // Use canvas bounds if available, otherwise use viewport
    const boundLeft = canvasRect ? canvasRect.left + margin : margin;
    const boundRight = canvasRect ? canvasRect.right - margin : viewportWidth - margin;
    const boundTop = canvasRect ? canvasRect.top + margin : margin;
    const boundBottom = canvasRect ? canvasRect.bottom - margin : viewportHeight - margin;

    // Ensure we have enough space for the dropdown
    const availableWidth = boundRight - boundLeft;
    const availableHeight = boundBottom - boundTop;

    if (availableWidth < dropdownWidth || availableHeight < dropdownHeight) {
      // Canvas is too small - center dropdown as best as possible
      const dropdownX = Math.max(boundLeft, boundLeft + (availableWidth - dropdownWidth) / 2);
      const dropdownY = Math.max(boundTop, boundTop + (availableHeight - dropdownHeight) / 2);
      return {
        x: dropdownX,
        y: dropdownY,
        position: 'center-constrained',
        productCenter: { x: productCenterX, y: productCenterY }
      };
    }

    // Calculate intersection point between mouse cursor and product box edges
    const calculateIntersection = () => {
      // Calculate distances from mouse to each edge of the product box
      const distanceToRight = Math.abs(mouseX - rect.right);
      const distanceToLeft = Math.abs(mouseX - rect.left);
      const distanceToBottom = Math.abs(mouseY - rect.bottom);
      const distanceToTop = Math.abs(mouseY - rect.top);

      // Find the closest edge
      const minDistance = Math.min(distanceToRight, distanceToLeft, distanceToBottom, distanceToTop);

      // Calculate intersection point based on closest edge
      if (minDistance === distanceToRight) {
        // Closest to right edge - intersection at right edge, mouse Y
        return { x: rect.right, y: Math.max(rect.top, Math.min(mouseY, rect.bottom)), edge: 'right' };
      } else if (minDistance === distanceToLeft) {
        // Closest to left edge - intersection at left edge, mouse Y
        return { x: rect.left, y: Math.max(rect.top, Math.min(mouseY, rect.bottom)), edge: 'left' };
      } else if (minDistance === distanceToBottom) {
        // Closest to bottom edge - intersection at mouse X, bottom edge
        return { x: Math.max(rect.left, Math.min(mouseX, rect.right)), y: rect.bottom, edge: 'bottom' };
      } else {
        // Closest to top edge - intersection at mouse X, top edge
        return { x: Math.max(rect.left, Math.min(mouseX, rect.right)), y: rect.top, edge: 'top' };
      }
    };

    const intersection = calculateIntersection();
    let dropdownX, dropdownY, position;

    // Position dropdown based on intersection point and edge
    // Smart positioning priority: right → left → below → above
    if (intersection.edge === 'right') {
      // Position dropdown to the right of intersection point
      const rightX = intersection.x;
      if (rightX + dropdownWidth <= boundRight) {
        dropdownX = rightX;
        dropdownY = Math.max(boundTop, Math.min(intersection.y - dropdownHeight / 2, boundBottom - dropdownHeight));
        position = 'right';
      } else {
        // Fallback to left positioning
        dropdownX = intersection.x - dropdownWidth;
        dropdownY = Math.max(boundTop, Math.min(intersection.y - dropdownHeight / 2, boundBottom - dropdownHeight));
        position = 'left';
      }
    } else if (intersection.edge === 'left') {
      // Position dropdown to the left of intersection point
      const leftX = intersection.x - dropdownWidth;
      if (leftX >= boundLeft) {
        dropdownX = leftX;
        dropdownY = Math.max(boundTop, Math.min(intersection.y - dropdownHeight / 2, boundBottom - dropdownHeight));
        position = 'left';
      } else {
        // Fallback to right positioning
        dropdownX = intersection.x;
        dropdownY = Math.max(boundTop, Math.min(intersection.y - dropdownHeight / 2, boundBottom - dropdownHeight));
        position = 'right';
      }
    } else if (intersection.edge === 'bottom') {
      // Position dropdown below intersection point
      const belowY = intersection.y;
      if (belowY + dropdownHeight <= boundBottom) {
        dropdownY = belowY;
        dropdownX = Math.max(boundLeft, Math.min(intersection.x - dropdownWidth / 2, boundRight - dropdownWidth));
        position = 'below';
      } else {
        // Fallback to above positioning
        dropdownY = intersection.y - dropdownHeight;
        dropdownX = Math.max(boundLeft, Math.min(intersection.x - dropdownWidth / 2, boundRight - dropdownWidth));
        position = 'above';
      }
    } else { // intersection.edge === 'top'
      // Position dropdown above intersection point
      const aboveY = intersection.y - dropdownHeight;
      if (aboveY >= boundTop) {
        dropdownY = aboveY;
        dropdownX = Math.max(boundLeft, Math.min(intersection.x - dropdownWidth / 2, boundRight - dropdownWidth));
        position = 'above';
      } else {
        // Fallback to below positioning
        dropdownY = intersection.y;
        dropdownX = Math.max(boundLeft, Math.min(intersection.x - dropdownWidth / 2, boundRight - dropdownWidth));
        position = 'below';
      }
    }

    // Final safety checks to ensure dropdown is completely within bounds
    dropdownX = Math.max(boundLeft, Math.min(dropdownX, boundRight - dropdownWidth));
    dropdownY = Math.max(boundTop, Math.min(dropdownY, boundBottom - dropdownHeight));

    return {
      x: dropdownX,
      y: dropdownY,
      position,
      productCenter: { x: productCenterX, y: productCenterY },
      intersectionPoint: intersection
    };
  };

  const handleProductHover = (product, e) => {
    if (!isConnectMode && !isDraggingFromSidebar()) {
      // Clear any existing timeout
      if (hoverTimeoutRef.current) {
        clearTimeout(hoverTimeoutRef.current);
      }

      // Capture mouse coordinates immediately
      const rect = e.currentTarget.getBoundingClientRect();
      const canvasRect = canvasRef.current?.getBoundingClientRect();
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;
      const mouseX = e.clientX;
      const mouseY = e.clientY;

      // Add a small delay to prevent flickering when moving between product and buttons
      hoverTimeoutRef.current = setTimeout(() => {
        const positionData = calculateDropdownPosition(mouseX, mouseY, rect, canvasRect, viewportWidth, viewportHeight);

        // Set hover with smart positioning data
        setHoveredProduct({ ...product, dropdownPosition: positionData.position });
        setHoverPosition(positionData);
      }, 150); // Small delay to prevent flickering
    }
  };

  const handleProductLeave = () => {
    // Clear any existing timeout
    if (hoverTimeoutRef.current) {
      clearTimeout(hoverTimeoutRef.current);
    }

    // Add a delay before hiding the dropdown to allow moving to buttons
    hoverTimeoutRef.current = setTimeout(() => {
      setHoveredProduct(null);
    }, 200); // Small delay to allow moving to buttons
  };

  // Throttled drag handler for smooth performance
  const dragThrottleRef = useRef({});

  const handleDrag = (productId, e, data) => {
    // Hide dropdown during drag
    if (hoveredProduct?.id === productId) {
      setHoveredProduct(null);
    }

    // Throttle position updates for better performance
    const now = Date.now();
    if (!dragThrottleRef.current[productId] || now - dragThrottleRef.current[productId] > 16) { // ~60fps
      dragThrottleRef.current[productId] = now;
      onProductMove(productId, { x: data.x, y: data.y });
    }
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Delete' && selectedProducts.length > 0) {
      selectedProducts.forEach(productId => {
        onProductRemove(productId);
      });
    }
  };

  return (
    <div className="flex-1 relative overflow-hidden">
      <div
        ref={(el) => {
          canvasRef.current = el;
          if (ref) ref.current = el;
        }}
        className={`w-full h-full bg-dark-900 relative ${settings.showGrid ? 'canvas-grid' : ''}`}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onKeyDown={handleKeyDown}
        tabIndex={0}
      >
        {/* Connection Lines */}
        <svg className="absolute inset-0 w-full h-full pointer-events-none z-10">
          {connections.map(connection => {
            const fromProduct = products.find(p => p.id === connection.from);
            const toProduct = products.find(p => p.id === connection.to);

            if (!fromProduct || !toProduct) return null;

            return (
              <ConnectionLine
                key={connection.id}
                from={{
                  x: fromProduct.position.x + 50,
                  y: fromProduct.position.y + 35
                }}
                to={{
                  x: toProduct.position.x + 50,
                  y: toProduct.position.y + 35
                }}
              />
            );
          })}
        </svg>

        {/* Products */}
        {products.map(product => {
          const isDragging = draggingItems.has(product.id);

          return (
            <Draggable
              key={product.id}
              position={product.position}
              onDrag={(e, data) => handleDrag(product.id, e, data)}
              onStart={(e, data) => {
                // Hide dropdown when drag starts
                if (hoveredProduct?.id === product.id) {
                  setHoveredProduct(null);
                }

                // Add to dragging items for visual feedback
                setDraggingItems(prev => new Set(prev).add(product.id));

                // Start drag operation from canvas
                startDrag('canvas', product);
              }}
              onStop={(e, data) => {
                // Remove from dragging items
                setDraggingItems(prev => {
                  const newSet = new Set(prev);
                  newSet.delete(product.id);
                  return newSet;
                });

                // End drag operation
                endDrag();
              }}
              bounds="parent"
              enableUserSelectHack={false}
              scale={1}
            >
            <div
              className={`product-node group absolute z-20 transition-all duration-150 ease-out ${
                selectedProducts.includes(product.id)
                  ? 'ring-2 ring-primary-500 border-primary-500'
                  : ''
              } ${
                isConnectMode
                  ? 'cursor-pointer hover:ring-2 hover:ring-primary-400'
                  : 'cursor-move'
              } ${
                isDragging
                  ? 'opacity-80 scale-105 shadow-2xl ring-4 ring-primary-400 ring-opacity-50 z-50'
                  : 'hover:shadow-lg'
              }`}
              onClick={(e) => handleProductClick(product.id, e)}
              onMouseEnter={(e) => handleProductHover(product, e)}
              onMouseLeave={handleProductLeave}
              onMouseMove={(e) => {
                // Only update position if dropdown is already visible and we're not over a button
                if (hoveredProduct?.id === product.id && !e.target.closest('button')) {
                  // Throttle position updates to reduce flickering
                  const now = Date.now();
                  if (!product._lastPositionUpdate || now - product._lastPositionUpdate > 100) {
                    const rect = e.currentTarget.getBoundingClientRect();
                    const canvasRect = canvasRef.current?.getBoundingClientRect();
                    const viewportWidth = window.innerWidth;
                    const viewportHeight = window.innerHeight;

                    // Use current mouse coordinates for positioning
                    const mouseX = e.clientX;
                    const mouseY = e.clientY;

                    const positionData = calculateDropdownPosition(mouseX, mouseY, rect, canvasRect, viewportWidth, viewportHeight);

                    setHoveredProduct({ ...hoveredProduct, dropdownPosition: positionData.position, _lastPositionUpdate: now });
                    setHoverPosition(positionData);
                  }
                }
              }}
              style={{ width: '100px', height: '70px' }}
            >
              <ProductPNGRenderer
                product={product}
                width={100}
                height={70}
                className=""
              />

              {/* Product Label */}
              <div className="absolute top-full left-1/2 transform -translate-x-1/2 mt-2 text-xs text-center text-white bg-dark-800 border border-dark-600 rounded px-2 py-1 break-words shadow-sm min-w-24 max-w-48" style={{ lineHeight: '1.3', wordWrap: 'break-word', overflowWrap: 'break-word' }}>
                {product.name}
              </div>

              {/* Rotation Button (visible on hover) */}
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  e.preventDefault();

                  // Clear hover state if this product is currently hovered
                  if (hoveredProduct?.id === product.id) {
                    setHoveredProduct(null);
                  }

                  // Rotate the product
                  onProductRotate(product.id);
                }}
                onMouseEnter={(e) => {
                  // Keep the dropdown visible when hovering over the button
                  e.stopPropagation();
                  // Clear any pending hide timeout
                  if (hoverTimeoutRef.current) {
                    clearTimeout(hoverTimeoutRef.current);
                  }
                }}
                onMouseLeave={(e) => {
                  // Don't immediately hide dropdown when leaving button
                  e.stopPropagation();
                }}
                className="absolute -top-2 -left-2 w-5 h-5 bg-blue-500 hover:bg-blue-600 rounded-full flex items-center justify-center text-white text-xs opacity-0 group-hover:opacity-100 hover:!opacity-100 transition-opacity z-30"
                title={t('rotate') || 'Rotate'}
              >
                <RotateCw className="w-3 h-3" />
              </button>

              {/* Delete Button (visible on hover) */}
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  e.preventDefault();

                  // Clear hover state if this product is currently hovered
                  if (hoveredProduct?.id === product.id) {
                    setHoveredProduct(null);
                  }

                  // Remove the product
                  onProductRemove(product.id);
                }}
                onMouseEnter={(e) => {
                  // Keep the dropdown visible when hovering over the button
                  e.stopPropagation();
                  // Clear any pending hide timeout
                  if (hoverTimeoutRef.current) {
                    clearTimeout(hoverTimeoutRef.current);
                  }
                }}
                onMouseLeave={(e) => {
                  // Don't immediately hide dropdown when leaving button
                  e.stopPropagation();
                }}
                className="absolute -top-2 -right-2 w-5 h-5 bg-red-500 hover:bg-red-600 rounded-full flex items-center justify-center text-white text-xs opacity-0 group-hover:opacity-100 hover:!opacity-100 transition-opacity z-30"
                title="Remove"
              >
                ×
              </button>
            </div>
          </Draggable>
          );
        })}

        {/* Instructions */}
        {products.length === 0 && (
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-center text-dark-400">
              <div className="text-6xl mb-4">🔧</div>
              <h3 className="text-xl font-semibold mb-2">{t('startBuilding')}</h3>
              <p className="text-sm">
                {t('dragComponents')}
              </p>
              <div className="mt-4 text-xs space-y-1">
                <p>• {t('instructions.drag')}</p>
                <p>• {t('instructions.connect')}</p>
                <p>• {t('instructions.hover')}</p>
              </div>
            </div>
          </div>
        )}

        {/* Connect Mode Instructions */}
        {isConnectMode && (
          <div className="absolute top-4 left-4 bg-primary-600 text-white px-4 py-2 rounded-lg shadow-lg">
            <div className="text-sm font-medium">{t('connectModeActive')}</div>
            <div className="text-xs opacity-90">{t('connectModeInstructions')}</div>
          </div>
        )}

        {/* Selection Overlay */}
        {isSelectionMode && (
          <SelectionOverlay
            onConfirm={handleSelectionConfirm}
            onCancel={onSelectionCancel}
            onClear={onSelectionClear}
            canvasRect={canvasRef.current?.getBoundingClientRect()}
          />
        )}
      </div>

      {/* Product Details Dropdown - Primary dropdown with smart boundary detection */}
      {hoveredProduct && products.find(p => p.id === hoveredProduct.id) && !isSelectionMode && (
        <ProductDetails
          product={hoveredProduct}
          position={hoverPosition}
        />
      )}
    </div>
  );
});

Canvas.displayName = 'Canvas';

export default Canvas;
