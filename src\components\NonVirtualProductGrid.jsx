import React, { useRef, useEffect, useState } from 'react';
import { ChevronDown, ChevronRight } from 'lucide-react';
import ProductItem from './ProductItem';
import HighlightedText from './HighlightedText';
import { useLanguage } from '../contexts/LanguageContext';

/**
 * Non-virtual product grid component - fallback for when virtual scrolling fails
 * This renders all products without virtualization
 */
const NonVirtualProductGrid = ({
  groupedProducts,
  expandedCategories,
  shouldAutoExpand,
  searchTerm,
  onToggleCategory,
  onToggleSubcategory,
  onProductDragStart
}) => {
  const { t, tc } = useLanguage();
  const scrollContainerRef = useRef(null);
  const [isScrollable, setIsScrollable] = useState(false);
  const [showScrollIndicator, setShowScrollIndicator] = useState(false);
  const [scrollTop, setScrollTop] = useState(0);

  // Check if content is scrollable and handle scroll indicators
  useEffect(() => {
    const checkScrollable = () => {
      if (scrollContainerRef.current) {
        const { scrollHeight, clientHeight, scrollTop } = scrollContainerRef.current;
        const isContentScrollable = scrollHeight > clientHeight;
        setIsScrollable(isContentScrollable);

        // Show scroll indicator when there's more content below
        const isNearBottom = scrollTop + clientHeight >= scrollHeight - 10;
        setShowScrollIndicator(isContentScrollable && !isNearBottom);
      }
    };

    checkScrollable();

    // Check on content changes
    const observer = new ResizeObserver(checkScrollable);
    if (scrollContainerRef.current) {
      observer.observe(scrollContainerRef.current);
    }

    return () => observer.disconnect();
  }, [groupedProducts, expandedCategories]);

  // Handle scroll events for smooth scrolling and indicators
  const handleScroll = (e) => {
    const { scrollHeight, clientHeight, scrollTop } = e.target;
    const isNearBottom = scrollTop + clientHeight >= scrollHeight - 10;
    setShowScrollIndicator(isScrollable && !isNearBottom);
    setScrollTop(scrollTop);
  };

  // Smooth scroll to top when search changes
  useEffect(() => {
    if (scrollContainerRef.current && searchTerm) {
      scrollContainerRef.current.scrollTo({
        top: 0,
        behavior: 'smooth'
      });
    }
  }, [searchTerm]);

  // Handle empty state
  if (Object.keys(groupedProducts).length === 0) {
    return (
      <div className="absolute inset-0 flex items-center justify-center">
        <div className="text-center text-dark-400 py-8">
          <div className="w-8 h-8 mx-auto mb-2 opacity-50">
            <svg viewBox="0 0 24 24" fill="currentColor">
              <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
            </svg>
          </div>
          <p>{t('noProductsFound')}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="relative h-full">
      {/* Scrollable Content */}
      <div
        ref={scrollContainerRef}
        className="h-full overflow-y-auto sidebar-scroll p-4"
        onScroll={handleScroll}
        style={{
          // Enable momentum scrolling on iOS
          WebkitOverflowScrolling: 'touch',
          // Improve scrolling performance
          willChange: 'scroll-position',
          // Prevent overscroll bounce
          overscrollBehavior: 'contain',
          // Ensure height constraint
          maxHeight: '100%'
        }}
      >
        {Object.entries(groupedProducts).map(([category, subcategories]) => (
          <div key={category} className="mb-4">
            {/* Category Header */}
            <button
              onClick={() => onToggleCategory(category)}
              className="w-full flex items-center justify-between p-2 rounded-lg hover:bg-dark-700 transition-colors"
            >
              <HighlightedText
                text={tc(category)}
                searchTerm={searchTerm}
                className="font-medium text-white"
                highlightClassName="bg-yellow-400 text-yellow-900 px-1 rounded"
              />
              {(expandedCategories[category] || shouldAutoExpand) ? (
                <ChevronDown className="w-4 h-4 text-dark-400" />
              ) : (
                <ChevronRight className="w-4 h-4 text-dark-400" />
              )}
            </button>

            {/* Subcategories */}
            {(expandedCategories[category] || shouldAutoExpand) && (
              <div className="ml-4 mt-2 space-y-2">
                {Object.entries(subcategories).map(([subcategory, products]) => (
                  <div key={subcategory}>
                    {/* Subcategory Header */}
                    <button
                      onClick={() => onToggleSubcategory(category, subcategory)}
                      className="w-full flex items-center justify-between p-2 rounded-lg hover:bg-dark-700 transition-colors"
                    >
                      <HighlightedText
                        text={subcategory}
                        searchTerm={searchTerm}
                        className="text-sm text-dark-200"
                        highlightClassName="bg-yellow-400 text-yellow-900 px-1 rounded"
                      />
                      <div className="flex items-center gap-2">
                        <span className="text-xs text-dark-400 bg-dark-600 px-2 py-1 rounded">
                          {products.length}
                        </span>
                        {(expandedCategories[`${category}-${subcategory}`] || shouldAutoExpand) ? (
                          <ChevronDown className="w-3 h-3 text-dark-400" />
                        ) : (
                          <ChevronRight className="w-3 h-3 text-dark-400" />
                        )}
                      </div>
                    </button>

                    {/* Products */}
                    {(expandedCategories[`${category}-${subcategory}`] || shouldAutoExpand) && (
                      <div className="ml-4 mt-4 grid grid-cols-2 gap-4">
                        {products.map(product => (
                          <ProductItem
                            key={product.id}
                            product={product}
                            onDragStart={onProductDragStart}
                            searchTerm={searchTerm}
                          />
                        ))}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Scroll Indicator */}
      {showScrollIndicator && (
        <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 pointer-events-none">
          <div className="bg-dark-600 bg-opacity-90 text-white text-xs px-3 py-1 rounded-full shadow-lg animate-pulse">
            <div className="flex items-center gap-1">
              <span>Scroll for more</span>
              <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </div>
          </div>
        </div>
      )}

      {/* Scroll to Top Button (appears when scrolled down) */}
      {isScrollable && scrollTop > 200 && (
        <button
          onClick={() => {
            scrollContainerRef.current?.scrollTo({
              top: 0,
              behavior: 'smooth'
            });
          }}
          className="absolute bottom-4 right-4 bg-primary-600 hover:bg-primary-700 text-white p-2 rounded-full shadow-lg transition-colors z-10"
          title="Scroll to top"
        >
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clipRule="evenodd" />
          </svg>
        </button>
      )}
    </div>
  );
};

export default NonVirtualProductGrid;
