/**
 * Virtual scrolling utilities for efficient rendering of large lists
 */

/**
 * Calculate visible items in a virtual scroll container
 * @param {number} scrollTop - Current scroll position
 * @param {number} containerHeight - Height of the scroll container
 * @param {number} itemHeight - Height of each item
 * @param {number} totalItems - Total number of items
 * @param {number} overscan - Number of items to render outside viewport (buffer)
 * @returns {Object} Object with startIndex, endIndex, and visibleItems count
 */
export const calculateVisibleRange = (
  scrollTop,
  containerHeight,
  itemHeight,
  totalItems,
  overscan = 5
) => {
  const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan);
  const endIndex = Math.min(
    totalItems - 1,
    Math.ceil((scrollTop + containerHeight) / itemHeight) + overscan
  );
  
  return {
    startIndex,
    endIndex,
    visibleItems: endIndex - startIndex + 1
  };
};

/**
 * Calculate the total height needed for virtual scrolling
 * @param {number} totalItems - Total number of items
 * @param {number} itemHeight - Height of each item
 * @returns {number} Total height in pixels
 */
export const calculateTotalHeight = (totalItems, itemHeight) => {
  return totalItems * itemHeight;
};

/**
 * Calculate the offset for positioning visible items
 * @param {number} startIndex - Index of first visible item
 * @param {number} itemHeight - Height of each item
 * @returns {number} Offset in pixels
 */
export const calculateOffset = (startIndex, itemHeight) => {
  return startIndex * itemHeight;
};

/**
 * Flatten hierarchical product structure for virtual scrolling
 * This converts the category → subcategory → products structure into a flat list
 * while preserving the hierarchy information for rendering
 * @param {Object} groupedProducts - Products grouped by category and subcategory
 * @param {Object} expandedCategories - Which categories/subcategories are expanded
 * @param {boolean} shouldAutoExpand - Whether to auto-expand during search
 * @returns {Array} Flat array of items with type and hierarchy info
 */
export const flattenProductHierarchy = (groupedProducts, expandedCategories, shouldAutoExpand) => {
  const flatItems = [];

  Object.entries(groupedProducts).forEach(([category, subcategories]) => {
    // Add category header
    flatItems.push({
      type: 'category',
      id: `category-${category}`,
      category,
      data: category,
      isExpanded: expandedCategories[category] || shouldAutoExpand
    });

    // Add subcategories and products if category is expanded
    if (expandedCategories[category] || shouldAutoExpand) {
      Object.entries(subcategories).forEach(([subcategory, products]) => {
        // Add subcategory header
        flatItems.push({
          type: 'subcategory',
          id: `subcategory-${category}-${subcategory}`,
          category,
          subcategory,
          data: subcategory,
          productCount: products.length,
          isExpanded: expandedCategories[`${category}-${subcategory}`] || shouldAutoExpand
        });

        // Add products if subcategory is expanded
        if (expandedCategories[`${category}-${subcategory}`] || shouldAutoExpand) {
          // Group products into rows for 2-column grid
          const productRows = [];
          for (let i = 0; i < products.length; i += 2) {
            const rowProducts = products.slice(i, i + 2);
            productRows.push({
              type: 'product-row',
              id: `product-row-${category}-${subcategory}-${i}`,
              category,
              subcategory,
              products: rowProducts,
              rowIndex: i / 2
            });
          }
          flatItems.push(...productRows);
        }
      });
    }
  });

  return flatItems;
};

/**
 * Calculate item heights for different types of items
 * @param {string} itemType - Type of item ('category', 'subcategory', 'product-row')
 * @returns {number} Height in pixels
 */
export const getItemHeight = (itemType) => {
  switch (itemType) {
    case 'category':
      return 48; // Category header height (p-2 + text + margins)
    case 'subcategory':
      return 44; // Subcategory header height
    case 'product-row':
      return 120; // Product row height (image 64px + text + padding + gap)
    default:
      return 48;
  }
};

/**
 * Calculate the total height for a flattened item list
 * @param {Array} flatItems - Flattened items array
 * @returns {number} Total height in pixels
 */
export const calculateFlatListHeight = (flatItems) => {
  return flatItems.reduce((total, item) => {
    return total + getItemHeight(item.type);
  }, 0);
};

/**
 * Find visible items in a flattened list with variable heights
 * @param {Array} flatItems - Flattened items array
 * @param {number} scrollTop - Current scroll position
 * @param {number} containerHeight - Height of the scroll container
 * @param {number} overscan - Number of items to render outside viewport
 * @returns {Object} Object with visible items and positioning info
 */
export const findVisibleItems = (flatItems, scrollTop, containerHeight, overscan = 2) => {
  if (!flatItems.length || containerHeight <= 0) {
    return {
      visibleItems: [],
      startIndex: 0,
      endIndex: 0,
      offsetY: 0
    };
  }

  let currentY = 0;
  let startIndex = -1;
  let endIndex = -1;
  let offsetY = 0;

  // Find start index
  for (let i = 0; i < flatItems.length; i++) {
    const itemHeight = getItemHeight(flatItems[i].type);

    if (currentY + itemHeight > scrollTop && startIndex === -1) {
      startIndex = Math.max(0, i - overscan);
      offsetY = startIndex > 0 ?
        flatItems.slice(0, startIndex).reduce((sum, item) => sum + getItemHeight(item.type), 0) : 0;
      break;
    }
    currentY += itemHeight;
  }

  // Find end index - start from startIndex, not offsetY
  if (startIndex === -1) {
    startIndex = 0;
    offsetY = 0;
  }

  currentY = offsetY;
  for (let i = startIndex; i < flatItems.length; i++) {
    const itemHeight = getItemHeight(flatItems[i].type);

    if (currentY > scrollTop + containerHeight + (overscan * 50)) {
      endIndex = Math.min(flatItems.length - 1, i + overscan);
      break;
    }
    currentY += itemHeight;
  }

  if (endIndex === -1) endIndex = flatItems.length - 1;

  // Ensure we have valid indices
  startIndex = Math.max(0, Math.min(startIndex, flatItems.length - 1));
  endIndex = Math.max(startIndex, Math.min(endIndex, flatItems.length - 1));

  const visibleItems = flatItems.slice(startIndex, endIndex + 1);

  return {
    visibleItems,
    startIndex,
    endIndex,
    offsetY
  };
};

/**
 * Throttle function for scroll events
 * @param {Function} func - Function to throttle
 * @param {number} limit - Throttle limit in milliseconds
 * @returns {Function} Throttled function
 */
export const throttle = (func, limit) => {
  let inThrottle;
  return function() {
    const args = arguments;
    const context = this;
    if (!inThrottle) {
      func.apply(context, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
};
