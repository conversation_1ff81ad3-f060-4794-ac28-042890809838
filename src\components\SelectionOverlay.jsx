import React, { useState, useRef, useEffect } from 'react';
import { Check, X, RotateCcw } from 'lucide-react';
import { useLanguage } from '../contexts/LanguageContext';

const SelectionOverlay = ({ 
  onConfirm, 
  onCancel, 
  onClear,
  canvasRect 
}) => {
  const { t } = useLanguage();
  const [isSelecting, setIsSelecting] = useState(false);
  const [startPoint, setStartPoint] = useState(null);
  const [currentPoint, setCurrentPoint] = useState(null);
  const [selectionRect, setSelectionRect] = useState(null);
  const overlayRef = useRef(null);

  // Calculate selection rectangle from start and current points
  const calculateSelectionRect = (start, current) => {
    if (!start || !current) return null;
    
    const x = Math.min(start.x, current.x);
    const y = Math.min(start.y, current.y);
    const width = Math.abs(current.x - start.x);
    const height = Math.abs(current.y - start.y);
    
    return { x, y, width, height };
  };

  // Handle mouse down - start selection
  const handleMouseDown = (e) => {
    if (e.target !== overlayRef.current) return;
    
    const rect = overlayRef.current.getBoundingClientRect();
    const point = {
      x: e.clientX - rect.left,
      y: e.clientY - rect.top
    };
    
    setStartPoint(point);
    setCurrentPoint(point);
    setIsSelecting(true);
    setSelectionRect(null);
  };

  // Handle mouse move - update selection
  const handleMouseMove = (e) => {
    if (!isSelecting || !startPoint) return;
    
    const rect = overlayRef.current.getBoundingClientRect();
    const point = {
      x: e.clientX - rect.left,
      y: e.clientY - rect.top
    };
    
    setCurrentPoint(point);
    setSelectionRect(calculateSelectionRect(startPoint, point));
  };

  // Handle mouse up - finish selection
  const handleMouseUp = () => {
    if (isSelecting && startPoint && currentPoint) {
      const finalRect = calculateSelectionRect(startPoint, currentPoint);
      if (finalRect && finalRect.width > 10 && finalRect.height > 10) {
        setSelectionRect(finalRect);
      }
    }
    setIsSelecting(false);
  };

  // Handle confirm selection
  const handleConfirm = () => {
    if (selectionRect) {
      onConfirm(selectionRect);
    }
  };

  // Handle clear selection
  const handleClear = () => {
    setSelectionRect(null);
    setStartPoint(null);
    setCurrentPoint(null);
    setIsSelecting(false);
    onClear();
  };

  // Add event listeners
  useEffect(() => {
    const overlay = overlayRef.current;
    if (!overlay) return;

    overlay.addEventListener('mousedown', handleMouseDown);
    overlay.addEventListener('mousemove', handleMouseMove);
    overlay.addEventListener('mouseup', handleMouseUp);

    return () => {
      overlay.removeEventListener('mousedown', handleMouseDown);
      overlay.removeEventListener('mousemove', handleMouseMove);
      overlay.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isSelecting, startPoint]);

  // Current selection rectangle for display
  const displayRect = selectionRect || (isSelecting ? calculateSelectionRect(startPoint, currentPoint) : null);

  return (
    <div className="absolute inset-0 z-50" data-selection-overlay="true">
      {/* Semi-transparent overlay */}
      <div
        ref={overlayRef}
        className="absolute inset-0 bg-black bg-opacity-30 cursor-crosshair"
        style={{ cursor: 'crosshair' }}
      >
        {/* Selection rectangle */}
        {displayRect && displayRect.width > 0 && displayRect.height > 0 && (
          <div
            className="absolute border-2 border-dashed border-primary-400 bg-primary-100 bg-opacity-20"
            style={{
              left: displayRect.x,
              top: displayRect.y,
              width: displayRect.width,
              height: displayRect.height,
              pointerEvents: 'none'
            }}
          >
            {/* Selection dimensions display */}
            <div className="absolute -top-8 left-0 bg-primary-600 text-white px-2 py-1 rounded text-xs">
              {Math.round(displayRect.width)} × {Math.round(displayRect.height)}
            </div>
          </div>
        )}
      </div>

      {/* Instructions */}
      <div className="absolute top-4 left-1/2 transform -translate-x-1/2 bg-dark-800 text-white px-6 py-3 rounded-lg shadow-lg border border-dark-600">
        <div className="text-center">
          <div className="text-sm font-medium mb-1">
            {selectionRect ? t('Selection Ready') || 'Selection Ready' : t('Select Export Area') || 'Select Export Area'}
          </div>
          <div className="text-xs text-gray-300">
            {selectionRect 
              ? t('Confirm to export selected area') || 'Confirm to export selected area'
              : t('Click and drag to select an area') || 'Click and drag to select an area'
            }
          </div>
        </div>
      </div>

      {/* Action buttons */}
      <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex items-center gap-2">
        {selectionRect && (
          <button
            onClick={handleConfirm}
            className="flex items-center gap-2 px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors"
          >
            <Check className="w-4 h-4" />
            <span className="text-sm">{t('Confirm') || 'Confirm'}</span>
          </button>
        )}
        
        {selectionRect && (
          <button
            onClick={handleClear}
            className="flex items-center gap-2 px-4 py-2 bg-yellow-600 hover:bg-yellow-700 text-white rounded-lg transition-colors"
          >
            <RotateCcw className="w-4 h-4" />
            <span className="text-sm">{t('Clear') || 'Clear'}</span>
          </button>
        )}
        
        <button
          onClick={onCancel}
          className="flex items-center gap-2 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
        >
          <X className="w-4 h-4" />
          <span className="text-sm">{t('Cancel') || 'Cancel'}</span>
        </button>
      </div>
    </div>
  );
};

export default SelectionOverlay;
