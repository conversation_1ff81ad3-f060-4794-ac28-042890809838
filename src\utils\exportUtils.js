import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';

// Helper function to calculate extended selection rectangle that includes product labels
const calculateExtendedSelectionRect = (selectionRect, products) => {
  if (!products || products.length === 0) {
    return selectionRect;
  }

  // Product dimensions and label positioning constants
  const PRODUCT_WIDTH = 100;
  const PRODUCT_HEIGHT = 70;
  const LABEL_MARGIN_TOP = 8; // mt-2 in Tailwind = 8px
  const LABEL_PADDING_VERTICAL = 8; // py-1 in Tailwind = 4px top + 4px bottom
  const LABEL_LINE_HEIGHT = 1.3;
  const LABEL_FONT_SIZE = 12; // text-xs = 12px
  const MAX_LABEL_WIDTH = 192; // max-w-48 in Tailwind = 192px

  // Calculate estimated label height based on text length and width constraints
  const calculateLabelHeight = (productName) => {
    if (!productName) return LABEL_FONT_SIZE + LABEL_PADDING_VERTICAL;

    // Rough estimation: average character width for text-xs is about 6px
    const avgCharWidth = 6;
    const availableWidth = MAX_LABEL_WIDTH - 16; // subtract horizontal padding (px-2 = 8px each side)
    const charsPerLine = Math.floor(availableWidth / avgCharWidth);
    const estimatedLines = Math.ceil(productName.length / charsPerLine);

    return (LABEL_FONT_SIZE * LABEL_LINE_HEIGHT * estimatedLines) + LABEL_PADDING_VERTICAL;
  };

  // Find products that intersect with the selection rectangle
  const productsInSelection = products.filter(product => {
    const productRect = {
      x: product.position.x,
      y: product.position.y,
      width: PRODUCT_WIDTH,
      height: PRODUCT_HEIGHT
    };

    // Check if product rectangle intersects with selection rectangle
    return !(productRect.x + productRect.width < selectionRect.x ||
             productRect.x > selectionRect.x + selectionRect.width ||
             productRect.y + productRect.height < selectionRect.y ||
             productRect.y > selectionRect.y + selectionRect.height);
  });

  if (productsInSelection.length === 0) {
    return selectionRect;
  }

  // Calculate the maximum bottom extent of product labels within the selection
  let maxLabelBottom = selectionRect.y + selectionRect.height;

  productsInSelection.forEach(product => {
    // Calculate where the product label ends using dynamic height calculation
    const productBottom = product.position.y + PRODUCT_HEIGHT;
    const labelHeight = calculateLabelHeight(product.name);
    const labelBottom = productBottom + LABEL_MARGIN_TOP + labelHeight;

    // Only extend if the label extends beyond the current selection
    if (labelBottom > maxLabelBottom) {
      maxLabelBottom = labelBottom;
    }
  });

  // Return extended selection rectangle
  return {
    x: selectionRect.x,
    y: selectionRect.y,
    width: selectionRect.width,
    height: maxLabelBottom - selectionRect.y
  };
};

// Export canvas as image
export const exportAsImage = async (canvasElement) => {
  try {
    const canvas = await html2canvas(canvasElement, {
      backgroundColor: '#0f172a',
      scale: 2,
      useCORS: true,
      allowTaint: true
    });

    const link = document.createElement('a');
    link.download = `plumber-plan-${new Date().toISOString().split('T')[0]}.png`;
    link.href = canvas.toDataURL();
    link.click();
  } catch (error) {
    console.error('Error exporting image:', error);
    alert('Failed to export image. Please try again.');
  }
};

// Export selected area of canvas as image
export const exportSelectedArea = async (canvasElement, selectionRect, products = []) => {
  try {
    // Find and temporarily hide the selection overlay to prevent it from appearing in the export
    const selectionOverlay = document.querySelector('[data-selection-overlay="true"]');
    const originalDisplay = selectionOverlay ? selectionOverlay.style.display : null;

    if (selectionOverlay) {
      selectionOverlay.style.display = 'none';
    }

    // Small delay to ensure the overlay is hidden before capture
    await new Promise(resolve => setTimeout(resolve, 100));

    // Calculate extended selection rectangle to include product labels
    const extendedSelectionRect = calculateExtendedSelectionRect(selectionRect, products);

    // Capture the entire canvas without the selection overlay
    const fullCanvas = await html2canvas(canvasElement, {
      backgroundColor: '#0f172a',
      scale: 2,
      useCORS: true,
      allowTaint: true,
      ignoreElements: (element) => {
        // Ignore any elements that are selection-related
        return element.getAttribute('data-selection-overlay') === 'true' ||
               element.closest('[data-selection-overlay="true"]') !== null;
      }
    });

    // Restore the selection overlay
    if (selectionOverlay && originalDisplay !== null) {
      selectionOverlay.style.display = originalDisplay;
    } else if (selectionOverlay) {
      selectionOverlay.style.display = '';
    }

    // Create a new canvas for the cropped area
    const croppedCanvas = document.createElement('canvas');
    const ctx = croppedCanvas.getContext('2d');

    // Calculate the actual dimensions considering the scale factor
    const scale = 2;
    const cropX = extendedSelectionRect.x * scale;
    const cropY = extendedSelectionRect.y * scale;
    const cropWidth = extendedSelectionRect.width * scale;
    const cropHeight = extendedSelectionRect.height * scale;

    // Set the cropped canvas dimensions
    croppedCanvas.width = cropWidth;
    croppedCanvas.height = cropHeight;

    // Draw the selected area onto the new canvas
    ctx.drawImage(
      fullCanvas,
      cropX, cropY, cropWidth, cropHeight,
      0, 0, cropWidth, cropHeight
    );

    // Download the cropped image
    const link = document.createElement('a');
    link.download = `plumber-plan-selection-${new Date().toISOString().split('T')[0]}.png`;
    link.href = croppedCanvas.toDataURL();
    link.click();
  } catch (error) {
    console.error('Error exporting selected area:', error);
    alert('Failed to export selected area. Please try again.');

    // Ensure overlay is restored even if there's an error
    const selectionOverlay = document.querySelector('[data-selection-overlay="true"]');
    if (selectionOverlay) {
      selectionOverlay.style.display = '';
    }
  }
};

// Aggregate identical products by their original product ID and properties
const aggregateProducts = (canvasProducts, includePricing = false) => {
  const productMap = new Map();

  canvasProducts.forEach(product => {
    // Extract the original product ID (remove the timestamp suffix)
    const originalId = product.id.split('-').slice(0, -1).join('-');

    // Create a unique key based on product properties
    const productKey = `${originalId}-${product.name}-${product.diameter || ''}-${product.material || ''}`;

    if (productMap.has(productKey)) {
      // Increment quantity for existing product
      productMap.get(productKey).quantity += 1;
    } else {
      // Add new product with quantity 1
      const productData = {
        originalId: originalId,
        name: product.name || 'Unnamed Product',
        diameter: product.diameter || product.size || 'N/A',
        material: product.material || 'N/A',
        category: product.category || 'N/A',
        subcategory: product.subcategory || 'N/A',
        quantity: 1
      };

      // Add pricing information if requested
      if (includePricing) {
        productData.price = product.price || 0;
        productData.lineTotal = productData.quantity * productData.price;
      }

      productMap.set(productKey, productData);
    }
  });

  // Return aggregated products
  const aggregatedProducts = Array.from(productMap.values());

  return aggregatedProducts;
};

// Export enhanced report as PDF with custom titles and pricing
export const exportEnhancedReport = (reportData) => {
  try {
    const { aggregatedProducts, summary, customTitles } = reportData;

    const doc = new jsPDF({
      orientation: 'portrait',
      unit: 'mm',
      format: 'a4'
    });

    // Set font encoding for better Unicode support
    doc.setFont('helvetica');

    // Professional Invoice Header - matching reference image
    doc.setFontSize(36);
    doc.setFont('helvetica', 'bold');
    doc.setTextColor(70, 70, 70); // Dark gray color
    doc.text(customTitles?.title || 'Invoice', 20, 35);

    // Reset color to black for other text
    doc.setTextColor(0, 0, 0);

    // Header information section - simplified layout
    const headerY = 60;

    // Right column - Date and Invoice details only
    doc.setFont('helvetica', 'bold');
    doc.setFontSize(11);
    doc.text('Date:', 130, headerY);
    doc.text('Invoice #:', 130, headerY + 15);

    doc.setFont('helvetica', 'normal');
    doc.setFontSize(10);
    doc.text(new Date().toLocaleDateString(), 150, headerY);
    doc.text(summary.reportNumber || `RPT-${Date.now()}`, 150, headerY + 15);

    // Professional table design - matching reference image
    const tableStartY = headerY + 40;
    let yPosition = tableStartY;

    // Draw table border and header
    const tableWidth = 170;
    const tableLeft = 20;
    const tableRight = tableLeft + tableWidth;

    // Table header background (light gray)
    doc.setFillColor(245, 245, 245);
    doc.rect(tableLeft, yPosition - 8, tableWidth, 12, 'F');

    // Table header borders
    doc.setLineWidth(0.5);
    doc.setDrawColor(0, 0, 0);
    doc.rect(tableLeft, yPosition - 8, tableWidth, 12);

    // Column dividers in header
    const col1Width = 85; // Description column
    const col2Width = 25; // Quantity column
    const col3Width = 30; // Unit Price column
    const col4Width = 30; // Total column

    doc.line(tableLeft + col1Width, yPosition - 8, tableLeft + col1Width, yPosition + 4);
    doc.line(tableLeft + col1Width + col2Width, yPosition - 8, tableLeft + col1Width + col2Width, yPosition + 4);
    doc.line(tableLeft + col1Width + col2Width + col3Width, yPosition - 8, tableLeft + col1Width + col2Width + col3Width, yPosition + 4);

    // Table header text
    doc.setFont('helvetica', 'bold');
    doc.setFontSize(10);
    doc.setTextColor(0, 0, 0);
    doc.text('Description', tableLeft + 2, yPosition);
    doc.text('Quantity', tableLeft + col1Width + 8, yPosition);
    doc.text('Unit Price', tableLeft + col1Width + col2Width + 5, yPosition);
    doc.text('Total', tableLeft + col1Width + col2Width + col3Width + 8, yPosition);

    yPosition += 12;

    // Reset font to normal
    doc.setFont('helvetica', 'normal');
    doc.setFontSize(10);

    let grandTotal = 0;
    let rowCount = 0;
    const maxRowsPerPage = 12; // Limit rows to maintain professional spacing

    // Enhanced products with pricing - professional table rows
    aggregatedProducts.forEach((product, index) => {
      // Check if we need a new page
      if (rowCount >= maxRowsPerPage || yPosition > 240) {
        doc.addPage();
        yPosition = 30;
        rowCount = 0;

        // Repeat professional table header on new page
        doc.setFillColor(245, 245, 245);
        doc.rect(tableLeft, yPosition - 8, tableWidth, 12, 'F');
        doc.setLineWidth(0.5);
        doc.rect(tableLeft, yPosition - 8, tableWidth, 12);

        // Column dividers
        doc.line(tableLeft + col1Width, yPosition - 8, tableLeft + col1Width, yPosition + 4);
        doc.line(tableLeft + col1Width + col2Width, yPosition - 8, tableLeft + col1Width + col2Width, yPosition + 4);
        doc.line(tableLeft + col1Width + col2Width + col3Width, yPosition - 8, tableLeft + col1Width + col2Width + col3Width, yPosition + 4);

        // Header text
        doc.setFont('helvetica', 'bold');
        doc.text('Description', tableLeft + 2, yPosition);
        doc.text('Quantity', tableLeft + col1Width + 8, yPosition);
        doc.text('Unit Price', tableLeft + col1Width + col2Width + 5, yPosition);
        doc.text('Total', tableLeft + col1Width + col2Width + col3Width + 8, yPosition);

        yPosition += 12;
        doc.setFont('helvetica', 'normal');
      }

      const rowHeight = 15;

      // Draw row border
      doc.setLineWidth(0.3);
      doc.setDrawColor(200, 200, 200);
      doc.rect(tableLeft, yPosition - 3, tableWidth, rowHeight);

      // Column dividers for this row
      doc.line(tableLeft + col1Width, yPosition - 3, tableLeft + col1Width, yPosition + 12);
      doc.line(tableLeft + col1Width + col2Width, yPosition - 3, tableLeft + col1Width + col2Width, yPosition + 12);
      doc.line(tableLeft + col1Width + col2Width + col3Width, yPosition - 3, tableLeft + col1Width + col2Width + col3Width, yPosition + 12);

      // Product description with proper text wrapping
      const productName = product.name || 'Unnamed Product';
      const maxDescriptionWidth = col1Width - 4;

      // Split text to fit in description column
      const splitText = doc.splitTextToSize(productName, maxDescriptionWidth);
      const displayText = splitText.length > 2 ? splitText.slice(0, 2) : splitText;
      if (splitText.length > 2) {
        displayText[1] = displayText[1].substring(0, displayText[1].length - 3) + '...';
      }

      doc.setFont('helvetica', 'normal');
      doc.setFontSize(9);
      displayText.forEach((line, lineIndex) => {
        doc.text(line, tableLeft + 2, yPosition + 3 + (lineIndex * 4));
      });

      // Product details on second line if space allows
      if (displayText.length === 1) {
        doc.setFontSize(8);
        doc.setTextColor(100, 100, 100);
        const details = `${product.diameter || 'N/A'} • ${product.material || 'N/A'} • ${product.category || 'N/A'}`;
        const detailsText = doc.splitTextToSize(details, maxDescriptionWidth);
        if (detailsText[0]) {
          doc.text(detailsText[0], tableLeft + 2, yPosition + 8);
        }
        doc.setTextColor(0, 0, 0);
      }

      // Pricing information
      const unitPrice = product.price || 0;
      const quantity = product.quantity || 0;
      const lineTotal = product.lineTotal || (unitPrice * quantity);

      grandTotal += lineTotal;

      // Quantity (centered in column)
      doc.setFontSize(10);
      doc.text(quantity.toString(), tableLeft + col1Width + (col2Width / 2), yPosition + 6, { align: 'center' });

      // Unit Price (right-aligned in column)
      doc.text(unitPrice.toFixed(2), tableLeft + col1Width + col2Width + col3Width - 2, yPosition + 6, { align: 'right' });

      // Line Total (right-aligned in column)
      doc.text(lineTotal.toFixed(2), tableLeft + col1Width + col2Width + col3Width + col4Width - 2, yPosition + 6, { align: 'right' });

      yPosition += rowHeight;
      rowCount++;
    });

    // Close the table with bottom border
    doc.setLineWidth(0.5);
    doc.setDrawColor(0, 0, 0);
    doc.line(tableLeft, yPosition - 3, tableRight, yPosition - 3);

    // Professional footer section - simplified
    yPosition += 20;

    // Total Amount Due section (right-aligned)
    doc.setFont('helvetica', 'bold');
    doc.setFontSize(12);
    doc.text('Total Amount Due:', 130, yPosition);
    doc.setFontSize(14);
    doc.text(grandTotal.toFixed(2), 190, yPosition, { align: 'right' });

    // Professional footer - matching reference image style
    const pageHeight = 297; // A4 height in mm
    const footerY = pageHeight - 20;

    // Footer branding (bottom of page)
    doc.setFontSize(8);
    doc.setTextColor(150, 150, 150);
    doc.text('Generated by Plumber Design App', 20, footerY);
    doc.text(`Generated on: ${new Date().toLocaleString()}`, 20, footerY + 5);

    const fileName = `invoice-${new Date().toISOString().split('T')[0]}.pdf`;
    doc.save(fileName);

  } catch (error) {
    console.error('Error exporting enhanced report:', error);
    alert('Failed to export enhanced report. Please try again.');
  }
};

// Export invoice as PDF with automatic product aggregation (original function)
export const exportAsInvoice = (canvasProducts) => {
  try {
    // Aggregate identical products without pricing
    const aggregatedProducts = aggregateProducts(canvasProducts, false);

    const doc = new jsPDF({
      orientation: 'portrait',
      unit: 'mm',
      format: 'a4'
    });

    // Set font encoding for better Unicode support
    doc.setFont('helvetica');

    // Header
    doc.setFontSize(20);
    doc.text('Plumbing Installation Report', 20, 30);

    doc.setFontSize(12);
    doc.text(`Date: ${new Date().toLocaleDateString()}`, 20, 45);
    doc.text(`Report #: RPT-${Date.now()}`, 20, 55);
    doc.text(`Total Items on Canvas: ${canvasProducts.length}`, 20, 65);
    doc.text(`Unique Product Types: ${aggregatedProducts.length}`, 20, 75);

    // Table header with improved positioning (removed pricing columns)
    let yPosition = 95;
    doc.setFontSize(10);
    doc.setFont(undefined, 'bold');
    doc.text('Product Name', 20, yPosition);
    doc.text('Quantity', 140, yPosition);

    // Draw line under header
    doc.line(20, yPosition + 3, 190, yPosition + 3);
    yPosition += 15;

    // Reset font to normal
    doc.setFont(undefined, 'normal');

    // Aggregated products (without pricing calculations)
    aggregatedProducts.forEach((product) => {
      // Check if we need a new page (accounting for potential multi-line product names)
      const estimatedRowHeight = product.name.length > 60 ? 30 : 18; // More accurate height estimation

      if (yPosition + estimatedRowHeight > 250) {
        doc.addPage();
        yPosition = 30;

        // Repeat header on new page (without pricing columns)
        doc.setFontSize(10);
        doc.setFont(undefined, 'bold');
        doc.text('Product Name', 20, yPosition);
        doc.text('Quantity', 140, yPosition);
        doc.line(20, yPosition + 3, 190, yPosition + 3);
        yPosition += 15;
        doc.setFont(undefined, 'normal');
      }

      // Store the starting yPosition for alignment
      const rowStartY = yPosition;
      let currentY = yPosition;

      // Product name with enhanced multi-line support
      const productName = product.name || 'Unnamed Product';

      // Enhanced text wrapping for better display
      const maxLineLength = 55; // Characters per line
      const maxLines = 3; // Allow up to 3 lines for very long names

      if (productName.length > maxLineLength) {
        // Split long product names into multiple lines with better word wrapping
        const words = productName.split(' ');
        let lines = [];
        let currentLine = '';

        for (let i = 0; i < words.length; i++) {
          const testLine = currentLine + (currentLine ? ' ' : '') + words[i];

          // Check if adding this word would exceed line length
          if (testLine.length > maxLineLength && currentLine !== '') {
            lines.push(currentLine);
            currentLine = words[i];

            // If we've reached max lines, add remaining words to last line
            if (lines.length >= maxLines - 1) {
              // Add remaining words to current line
              for (let j = i + 1; j < words.length; j++) {
                const remainingTest = currentLine + ' ' + words[j];
                if (remainingTest.length <= maxLineLength + 10) { // Allow slight overflow for last line
                  currentLine = remainingTest;
                } else {
                  currentLine += '...';
                  break;
                }
              }
              break;
            }
          } else {
            currentLine = testLine;
          }
        }

        // Add the last line
        if (currentLine) {
          lines.push(currentLine);
        }

        // Print each line
        lines.forEach((line, index) => {
          doc.text(line, 20, currentY + (index * 5));
        });

        // Update currentY to account for multi-line names
        currentY += (lines.length - 1) * 5;
      } else {
        // Single line product name
        doc.text(productName, 20, currentY);
      }

      // Display quantity only (no pricing information)
      const quantity = parseInt(product.quantity) || 0;

      // Quantity (center-aligned in column)
      doc.text(quantity.toString(), 145, rowStartY);

      // Update yPosition for next product
      yPosition = Math.max(currentY, rowStartY) + 12;

      // Add product details on next line if space allows
      if (yPosition < 240) {
        doc.setFontSize(8);
        doc.setTextColor(100);
        const details = `${product.diameter || 'N/A'} • ${product.material || 'N/A'} • ${product.category || 'N/A'}`;
        doc.text(details, 25, yPosition + 3);
        doc.setTextColor(0);
        doc.setFontSize(10);
        yPosition += 8;
      }

      // Removed grand total calculation for PDF generation

      // Add spacing between products
      yPosition += 5;
    });

    // Summary section
    yPosition += 10;
    doc.line(20, yPosition, 190, yPosition);
    yPosition += 10;

    // Summary details
    doc.setFontSize(10);
    doc.text(`Total Unique Products: ${aggregatedProducts.length}`, 20, yPosition);
    yPosition += 8;
    doc.text(`Total Items Required: ${canvasProducts.length}`, 20, yPosition);
    yPosition += 8;

    // Removed grand total display for PDF generation

    // Footer
    yPosition += 20;
    doc.setFontSize(8);
    doc.setFont(undefined, 'normal');
    doc.setTextColor(100);
    doc.text('Generated by Plumber Design App - Automatic Product Aggregation', 20, yPosition);
    doc.text(`Generated on: ${new Date().toLocaleString()}`, 20, yPosition + 8);

    doc.save(`plumber-report-aggregated-${new Date().toISOString().split('T')[0]}.pdf`);

    // PDF generated silently without success popup

  } catch (error) {
    console.error('Error exporting aggregated report:', error);
    alert('Failed to export report. Please try again.');
  }
};

// Get aggregated report data for preview or other uses (with pricing for enhanced modal)
export const getAggregatedInvoiceData = (canvasProducts) => {
  try {
    // Include pricing information for the enhanced modal
    const aggregatedProducts = aggregateProducts(canvasProducts, true);

    return {
      success: true,
      data: {
        aggregatedProducts,
        summary: {
          totalUniqueProducts: aggregatedProducts.length,
          totalItemsRequired: canvasProducts.length,
          reportNumber: `RPT-${Date.now()}`,
          date: new Date().toLocaleDateString(),
          timestamp: new Date().toISOString()
        }
      }
    };
  } catch (error) {
    console.error('Error generating aggregated report data:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

// Export aggregated invoice data as JSON for integration with other systems
export const exportAggregatedInvoiceData = (canvasProducts) => {
  try {
    const invoiceData = getAggregatedInvoiceData(canvasProducts);

    if (!invoiceData.success) {
      throw new Error(invoiceData.error);
    }

    const dataStr = JSON.stringify(invoiceData.data, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });

    const link = document.createElement('a');
    link.download = `plumber-report-data-${new Date().toISOString().split('T')[0]}.json`;
    link.href = URL.createObjectURL(dataBlob);
    link.click();

    URL.revokeObjectURL(link.href);

    // Show success message (without pricing information)
    alert(`Report data exported successfully!\n\nSummary:\n• ${invoiceData.data.summary.totalUniqueProducts} unique product types\n• ${invoiceData.data.summary.totalItemsRequired} total items`);

  } catch (error) {
    console.error('Error exporting aggregated report data:', error);
    alert('Failed to export report data. Please try again.');
  }
};

// Export plan as JSON
export const exportPlan = (products, connections) => {
  try {
    const planData = {
      version: '1.0',
      timestamp: new Date().toISOString(),
      products: products,
      connections: connections
    };
    
    const dataStr = JSON.stringify(planData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    
    const link = document.createElement('a');
    link.download = `plumber-plan-${new Date().toISOString().split('T')[0]}.json`;
    link.href = URL.createObjectURL(dataBlob);
    link.click();
    
    URL.revokeObjectURL(link.href);
  } catch (error) {
    console.error('Error exporting plan:', error);
    alert('Failed to export plan. Please try again.');
  }
};

// Import plan from JSON
export const importPlan = (file, callback) => {
  try {
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const planData = JSON.parse(e.target.result);
        
        // Validate plan data structure
        if (planData.products && planData.connections) {
          callback(planData);
        } else {
          throw new Error('Invalid plan file format');
        }
      } catch (parseError) {
        console.error('Error parsing plan file:', parseError);
        alert('Invalid plan file format. Please select a valid plan file.');
      }
    };
    reader.readAsText(file);
  } catch (error) {
    console.error('Error importing plan:', error);
    alert('Failed to import plan. Please try again.');
  }
};
