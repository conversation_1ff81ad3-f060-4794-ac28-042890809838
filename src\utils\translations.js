export const translations = {
  en: {
    // App Title
    appTitle: 'Plumber Design',

    // Toolbar
    undo: 'Undo',
    redo: 'Redo',
    connectMode: 'Connect Mode',
    clearCanvas: 'Clear Canvas',
    exportImage: 'Export Image',
    invoice: 'Invoice',
    export: 'Export',
    import: 'Import',
    rotate: 'Rotate',

    // Sidebar
    components: 'Components',
    searchComponents: 'Search components...',
    addProduct: 'Add Product',
    noProductsFound: 'No products found',

    // Canvas
    startBuilding: 'Start Building Your Plan',
    dragComponents: 'Drag components from the sidebar to create your plumbing installation',
    instructions: {
      drag: 'Drag products from sidebar to canvas',
      connect: 'Click "Connect Mode" to link components',
      hover: 'Hover over products to see details'
    },
    connectModeActive: 'Connect Mode Active',
    connectModeInstructions: 'Click two products to connect them',

    // Product Details
    price: 'Price',
    diameter: 'Diameter',
    material: 'Material',

    // Upload Modal
    addNewProduct: 'Add New Product',
    productName: 'Product Name',
    category: 'Category',
    subcategory: 'Subcategory',
    selectCategory: 'Select category',
    selectSubcategory: 'Select subcategory',
    enterProductName: 'Enter product name',
    validPriceRequired: 'Valid price is required',
    diameterRequired: 'Diameter is required',
    materialRequired: 'Material is required',
    productImage: 'Product Image',
    productImageRequired: 'Product image is required',
    clickToUpload: 'Click to upload image',
    takePhoto: 'Take Photo',
    cancel: 'Cancel',

    // Validation Messages
    productNameRequired: 'Product name is required',
    categoryRequired: 'Category is required',
    subcategoryRequired: 'Subcategory is required',
    categoryNameRequired: 'Category name is required',
    subcategoryNameRequired: 'Subcategory name is required',
    parentCategoryRequired: 'Parent category is required',
    categoryNameExists: 'Category name already exists',
    subcategoryNameExists: 'Subcategory name already exists in this category',

    // Category Creation
    addNewCategory: 'Add New Category',
    addNewSubcategory: 'Add New Subcategory',
    createCategory: 'Create Category',
    createSubcategory: 'Create Subcategory',
    categoryName: 'Category Name',
    subcategoryName: 'Subcategory Name',
    parentCategory: 'Parent Category',
    selectParentCategory: 'Select parent category',
    enterCategoryName: 'Enter category name',
    enterSubcategoryName: 'Enter subcategory name',
    categoryCreatedSuccessfully: 'Category created successfully',
    subcategoryCreatedSuccessfully: 'Subcategory created successfully',
    optional: 'Optional',
    defaultImageWillBeUsed: 'A default placeholder will be used if no image is provided',

    // Confirmation Dialog Messages
    confirmPermanentDeleteCategory: 'Permanently Delete Category',
    confirmPermanentDeleteCategoryMessage: 'Are you sure you want to permanently delete this category? This action cannot be undone and all products in this category will be permanently removed from the app.',
    confirmPermanentDeleteSubcategory: 'Permanently Delete Subcategory',
    confirmPermanentDeleteSubcategoryMessage: 'Are you sure you want to permanently delete this subcategory? This action cannot be undone and all products in this subcategory will be permanently removed.',
    confirmPermanentDeleteProduct: 'Permanently Delete Product',
    confirmPermanentDeleteProductMessage: 'Are you sure you want to permanently delete this product? This action cannot be undone and the product will be completely removed from the app.',
    confirmRemoveSubcategory: 'Confirm Remove Subcategory',
    confirmRemoveSubcategoryMessage: 'Are you sure you want to remove this subcategory? You can restore it later from the removed subcategories section.',
    confirmRemoveProduct: 'Confirm Remove Product',
    confirmRemoveProductMessage: 'Are you sure you want to remove this product? You can restore it later from the removed products section.',
    permanentDeleteWarning: 'WARNING: This action is irreversible!',
    permanentlyDelete: 'Permanently Delete',
    removeSubcategory: 'Remove Subcategory',
    removeProduct: 'Remove Product',

    // Removed Items Sections
    removedSubcategories: 'Removed Subcategories',
    removedProducts: 'Removed Products',
    removedSubcategoriesNote: 'Removed subcategories are hidden from the sidebar. Click the + button to restore them or the trash button to permanently delete them.',
    removedProductsNote: 'Removed products are hidden from the sidebar. Click the + button to restore them or the trash button to permanently delete them.',
    restoreSubcategory: 'Restore Subcategory',
    restoreProduct: 'Restore Product',
    permanentlyDeleteSubcategory: 'Permanently Delete Subcategory',
    permanentlyDeleteProduct: 'Permanently Delete Product',

    // Product Creation Messages
    productAddedSuccessfully: 'Product "{name}" added successfully!',
    newCategoryCreated: 'New category "{category}" created.',
    newSubcategoryAdded: 'New subcategory "{subcategory}" added.',
    productAvailableInSidebar: 'Product is now available in the sidebar under {category} → {subcategory}.',
    errorAddingProduct: 'Error adding product. Please try again.',

    // Product Editing
    editProduct: 'Edit Product',
    productDiameter: 'Diameter/Size',
    productMaterial: 'Material',
    productCategory: 'Category',
    productSubcategory: 'Subcategory',
    enterDiameter: 'e.g., 1/2 inch, 3/4 inch, 25mm',
    enterMaterial: 'e.g., PVC, Copper, Steel, Brass',
    selectCategory: 'Select category',
    selectSubcategory: 'Select subcategory',
    selectCategoryFirst: 'Please select a category first',
    diameterRequired: 'Diameter/size is required',
    materialRequired: 'Material is required',
    categoryRequired: 'Category is required',
    subcategoryRequired: 'Subcategory is required',
    productUpdatedSuccessfully: 'Product Updated Successfully',
    productCustomizedSuccessfully: 'Product Customized Successfully',
    productMovedToCategory: 'Product moved to',
    errorSavingProduct: 'Error Saving Product. Please Try Again.',

    // Export Messages
    exportSuccess: 'Export successful',
    exportError: 'Failed to export. Please try again.',

    // Selection Mode
    'Select Export Area': 'Select Export Area',
    'Selection Ready': 'Selection Ready',
    'Confirm to export selected area': 'Confirm to export selected area',
    'Click and drag to select an area': 'Click and drag to select an area',
    'Confirm': 'Confirm',
    'Clear': 'Clear',
    'Cancel Selection': 'Cancel Selection',

    // Language
    language: 'Language',
    english: 'English',
    french: 'Français',

    // Settings
    settings: 'Settings',
    dataStore: 'Data Store',
    categoryManagement: 'Category Management',
    hierarchicalCategoryManagement: 'Hierarchical Category Management',
    productHierarchy: 'Product Hierarchy',
    subcategories: 'subcategories',
    products: 'products',
    searchProductsCategories: 'Search products, categories, or subcategories...',
    clearSearch: 'Clear search',
    searchResults: 'Search Results',
    dataManagement: 'Data Management',
    applicationSettings: 'Application Settings',
    sessionManagement: 'Session Management',
    categoryVisibility: 'Category Visibility',
    activeCategories: 'Active Categories',
    removedCategories: 'Removed Categories',
    removedSubcategories: 'Removed Subcategories',
    removedProducts: 'Removed Products',
    hideCategory: 'Hide Category',
    showCategory: 'Show Category',
    removeCategory: 'Remove Category',
    restoreCategory: 'Restore Category',
    permanentlyDeleteCategory: 'Permanently Delete Category',
    removeSubcategory: 'Remove Subcategory',
    restoreSubcategory: 'Restore Subcategory',
    permanentlyDeleteSubcategory: 'Permanently Delete Subcategory',
    removeProduct: 'Remove Product',
    restoreProduct: 'Restore Product',
    permanentlyDeleteProduct: 'Permanently Delete Product',
    removedCategoriesNote: 'Removed categories are hidden from the sidebar. Click the + button to restore them or the trash button to permanently delete them.',
    resetToDefault: 'Reset to Default',
    saveCurrentProject: 'Save Current Project',
    enterProjectName: 'Enter project name...',
    save: 'Save',
    savedProjects: 'Saved Projects',
    noSavedProjects: 'No saved projects',
    loadProject: 'Load Project',
    deleteProject: 'Delete Project',
    importData: 'Import Data',
    exportData: 'Export Data',
    importFile: 'Import File',
    exportBackup: 'Export Backup',
    exportToExcel: 'Export to Excel',
    importFromExcel: 'Import from Excel',
    exportTemplate: 'Export Template',
    excelExportSuccess: 'Excel file exported successfully',
    excelImportSuccess: 'Products imported successfully',
    excelImportError: 'Error importing Excel file',
    templateExportSuccess: 'Template exported successfully',
    templateDescription: 'Download a sample Excel template with example products to help you format your import data correctly.',
    imageUrlOrBase64: 'Image (URL or base64)',
    processingImages: 'Processing images...',
    invalidImageUrl: 'Invalid image URL',
    invalidImageData: 'Invalid image data',
    importedWithImages: 'imported with images',
    importedWithoutImages: 'imported without images',
    productTemplateBuilder: 'Product Template Builder',
    buildTemplate: 'Build Template',
    addProduct: 'Add Product',
    editProduct: 'Edit Product',
    deleteProduct: 'Delete Product',
    clearAll: 'Clear All',
    uploadImage: 'Upload Image',
    imagePreview: 'Image Preview',
    removeImage: 'Remove Image',
    productName: 'Product Name',
    selectCategory: 'Select Category',
    selectSubcategory: 'Select Subcategory',
    enterPrice: 'Enter Price',
    enterSize: 'Enter Size/Diameter',
    enterMaterial: 'Enter Material',
    requiredField: 'Required field',
    optionalField: 'Optional field',
    productsInTemplate: 'Products in Template',
    noProductsAdded: 'No products added to template yet',
    addFirstProduct: 'Add your first product using the form above',
    duplicateProduct: 'Product with this name already exists',
    templateBuilderDescription: 'Create custom product templates with images for easy Excel import',
    processingImage: 'Processing image...',
    imageProcessed: 'Image processed successfully',
    imageTooLarge: 'Image file is too large (max 2MB)',
    unsupportedImageFormat: 'Unsupported image format',
    exportTemplateWithProducts: 'Export Template with Products',
    excelImportSuccess: 'Products imported successfully',
    excelImportError: 'Error importing Excel file',
    fileValidationError: 'File validation error',
    processingExcelFile: 'Processing Excel file...',
    noValidProductsFound: 'No valid products found',
    invalidExcelFormat: 'Invalid Excel file format',
    fileReadError: 'Failed to read file',
    categoryCreationError: 'Failed to create category',
    productCreationError: 'Failed to create product',
    dangerZone: 'Danger Zone',
    clearAllDataWarning: 'This will permanently delete all saved projects and reset the workspace.',
    confirmClearAllData: 'Are you sure you want to clear all data? This cannot be undone.',
    clearAllData: 'Clear All Data',
    clearAllDataTitle: 'Clear All Application Data',
    clearAllDataDescription: 'This action will permanently remove ALL data including default products and categories, leaving the application completely empty.',
    dataToBeCleared: 'The following data will be cleared:',
    savedProjects: 'Saved Projects',
    customProducts: 'Custom Products',
    customCategories: 'Custom Categories',
    'Default Products': 'Default Products',
    'Default Categories': 'Default Categories',
    modifiedSettings: 'Modified Settings',
    noDataToShow: 'No data to show',
    dataCleared: 'All data has been successfully cleared, including default products and categories',
    dataClearFailed: 'Failed to clear data',
    restoreDefaults: 'Restore Default Data',
    restoreDefaultsDescription: 'Restore default categories and products',
    defaultDataRestored: 'Default data has been restored',
    restoreDefaultsFailed: 'Failed to restore default data',
    undoAvailable: 'You can restore default data using the "Restore Default Data" button',
    'Clearing...': 'Clearing...',
    // Product editing translations
    'Edit Product': 'Edit Product',
    'Product name is required': 'Product name is required',
    'Category is required': 'Category is required',
    'Subcategory is required': 'Subcategory is required',
    'Price must be a valid number': 'Price must be a valid number',
    'Failed to update product': 'Failed to update product',
    'Product Name': 'Product Name',
    'Category': 'Category',
    'Subcategory': 'Subcategory',
    'Price': 'Price',
    'Size/Diameter': 'Size/Diameter',
    'Material': 'Material',
    'Product Image URL': 'Product Image URL',
    'Save Changes': 'Save Changes',
    'Updating...': 'Updating...',
    'Edit': 'Edit',
    'Enter product name': 'Enter product name',
    'Select category': 'Select category',
    'Select subcategory': 'Select subcategory',
    'e.g., 1/2", 3/4", 1"': 'e.g., 1/2", 3/4", 1"',
    'e.g., Copper, PVC, Steel': 'e.g., Copper, PVC, Steel',
    languageSettings: 'Language Settings',
    canvasSettings: 'Canvas Settings',
    showGrid: 'Show Grid',
    autoSaveSettings: 'Auto-save Settings',
    enableAutoSave: 'Enable Auto-save',
    autoSaveInterval: 'Auto-save Interval (seconds)',
    resetSettings: 'Reset Settings',
    resetSettingsWarning: 'This will reset all application settings to their default values.',
    confirmResetSettings: 'Are you sure you want to reset all settings?',
    resetToDefaults: 'Reset to Defaults',
    exitBehavior: 'Exit Behavior',
    confirmBeforeExit: 'Confirm before exit',
    saveOnExit: 'Auto-save on exit',
    currentSession: 'Current Session',
    productsOnCanvas: 'Products on canvas',
    connections: 'Connections',
    lastSaved: 'Last saved',
    never: 'Never',
    sessionActions: 'Session Actions',
    saveSession: 'Save Session',
    saveAndExit: 'Save & Exit',
    confirmDelete: 'Confirm Delete',
    confirmDeleteProject: 'Are you sure you want to delete this project? This action cannot be undone.',
    confirmRemoveCategory: 'Confirm Remove Category',
    confirmRemoveCategoryMessage: 'Are you sure you want to remove this category from the sidebar? You can restore it later from the removed categories section.',
    confirmPermanentDeleteCategory: 'Permanently Delete Category',
    confirmPermanentDeleteCategoryMessage: 'Are you sure you want to permanently delete this category? This action cannot be undone and all products in this category will be permanently removed from the app.',
    confirmRemoveSubcategory: 'Confirm Remove Subcategory',
    confirmRemoveSubcategoryMessage: 'Are you sure you want to remove this subcategory? You can restore it later from the removed subcategories section.',
    confirmPermanentDeleteSubcategory: 'Permanently Delete Subcategory',
    confirmPermanentDeleteSubcategoryMessage: 'Are you sure you want to permanently delete this subcategory? This action cannot be undone and all products in this subcategory will be permanently removed.',
    confirmRemoveProduct: 'Confirm Remove Product',
    confirmRemoveProductMessage: 'Are you sure you want to remove this product? You can restore it later from the removed products section.',
    confirmPermanentDeleteProduct: 'Permanently Delete Product',
    confirmPermanentDeleteProductMessage: 'Are you sure you want to permanently delete this product? This action cannot be undone and the product will be completely removed from the app.',
    permanentDeleteWarning: 'WARNING: This action is irreversible!',
    permanentlyDelete: 'Permanently Delete',
    delete: 'Delete',
    projectNameRequired: 'Project name is required',
    projectSaved: 'Project saved successfully',
    sessionSaved: 'Session saved successfully',
    confirmExitSession: 'Save current session and exit?',

    // Product Template Builder Integration
    'Product added to template and main catalog': 'Product added to template and main catalog',
    'Product added to template': 'Product added to template',
    'already exists in main catalog': 'already exists in main catalog',
    'Product updated in template': 'Product updated in template',
    'Error adding product to main catalog': 'Error adding product to main catalog',
    'In Main Catalog': 'In Main Catalog',
    'Already Exists': 'Already Exists',
    'Template Summary': 'Template Summary',
    'Total Products': 'Total Products',
    'Products with Images': 'Products with Images',
    'Categories': 'Categories',
    'Average Price': 'Average Price',
    'Ready for Canvas': 'Ready for Canvas',
    'With Image': 'With Image'
  },

  fr: {
    // App Title
    appTitle: 'Conception Plomberie',

    // Toolbar
    undo: 'Annuler',
    redo: 'Refaire',
    connectMode: 'Mode Connexion',
    clearCanvas: 'Effacer le Canevas',
    exportImage: 'Exporter Image',
    invoice: 'Facture',
    export: 'Exporter',
    import: 'Importer',
    rotate: 'Pivoter',

    // Sidebar
    components: 'Composants',
    searchComponents: 'Rechercher des composants...',
    addProduct: 'Ajouter Produit',
    noProductsFound: 'Aucun produit trouvé',

    // Canvas
    startBuilding: 'Commencez à Construire Votre Plan',
    dragComponents: 'Faites glisser les composants de la barre latérale pour créer votre installation de plomberie',
    instructions: {
      drag: 'Faites glisser les produits de la barre latérale vers le canevas',
      connect: 'Cliquez sur "Mode Connexion" pour lier les composants',
      hover: 'Survolez les produits pour voir les détails'
    },
    connectModeActive: 'Mode Connexion Actif',
    connectModeInstructions: 'Cliquez sur deux produits pour les connecter',

    // Product Details
    price: 'Prix',
    diameter: 'Diamètre',
    material: 'Matériau',

    // Upload Modal
    addNewProduct: 'Ajouter Nouveau Produit',
    productName: 'Nom du Produit',
    category: 'Catégorie',
    subcategory: 'Sous-catégorie',
    selectCategory: 'Sélectionner une catégorie',
    selectSubcategory: 'Sélectionner une sous-catégorie',
    enterProductName: 'Entrez le nom du produit',
    validPriceRequired: 'Un prix valide est requis',
    diameterRequired: 'Le diamètre est requis',
    materialRequired: 'Le matériau est requis',
    productImage: 'Image du Produit',
    productImageRequired: 'L\'image du produit est requise',
    clickToUpload: 'Cliquez pour télécharger une image',
    takePhoto: 'Prendre une Photo',
    cancel: 'Annuler',

    // Validation Messages
    productNameRequired: 'Le nom du produit est requis',
    categoryRequired: 'La catégorie est requise',
    subcategoryRequired: 'La sous-catégorie est requise',
    categoryNameRequired: 'Le nom de la catégorie est requis',
    subcategoryNameRequired: 'Le nom de la sous-catégorie est requis',
    parentCategoryRequired: 'La catégorie parent est requise',
    categoryNameExists: 'Le nom de la catégorie existe déjà',
    subcategoryNameExists: 'Le nom de la sous-catégorie existe déjà dans cette catégorie',

    // Category Creation
    addNewCategory: 'Ajouter Nouvelle Catégorie',
    addNewSubcategory: 'Ajouter Nouvelle Sous-catégorie',
    createCategory: 'Créer Catégorie',
    createSubcategory: 'Créer Sous-catégorie',
    categoryName: 'Nom de la Catégorie',
    subcategoryName: 'Nom de la Sous-catégorie',
    parentCategory: 'Catégorie Parent',
    selectParentCategory: 'Sélectionner la catégorie parent',
    enterCategoryName: 'Entrez le nom de la catégorie',
    enterSubcategoryName: 'Entrez le nom de la sous-catégorie',
    categoryCreatedSuccessfully: 'Catégorie créée avec succès',
    subcategoryCreatedSuccessfully: 'Sous-catégorie créée avec succès',
    optional: 'Optionnel',
    defaultImageWillBeUsed: 'Une image par défaut sera utilisée si aucune image n\'est fournie',

    // Confirmation Dialog Messages
    confirmPermanentDeleteCategory: 'Supprimer Définitivement la Catégorie',
    confirmPermanentDeleteCategoryMessage: 'Êtes-vous sûr de vouloir supprimer définitivement cette catégorie ? Cette action ne peut pas être annulée et tous les produits de cette catégorie seront définitivement supprimés de l\'application.',
    confirmPermanentDeleteSubcategory: 'Supprimer Définitivement la Sous-catégorie',
    confirmPermanentDeleteSubcategoryMessage: 'Êtes-vous sûr de vouloir supprimer définitivement cette sous-catégorie ? Cette action ne peut pas être annulée et tous les produits de cette sous-catégorie seront définitivement supprimés.',
    confirmPermanentDeleteProduct: 'Supprimer Définitivement le Produit',
    confirmPermanentDeleteProductMessage: 'Êtes-vous sûr de vouloir supprimer définitivement ce produit ? Cette action ne peut pas être annulée et le produit sera complètement supprimé de l\'application.',
    confirmRemoveSubcategory: 'Confirmer la Suppression de la Sous-catégorie',
    confirmRemoveSubcategoryMessage: 'Êtes-vous sûr de vouloir supprimer cette sous-catégorie ? Vous pourrez la restaurer plus tard depuis la section des sous-catégories supprimées.',
    confirmRemoveProduct: 'Confirmer la Suppression du Produit',
    confirmRemoveProductMessage: 'Êtes-vous sûr de vouloir supprimer ce produit ? Vous pourrez le restaurer plus tard depuis la section des produits supprimés.',
    permanentDeleteWarning: 'ATTENTION : Cette action est irréversible !',
    permanentlyDelete: 'Supprimer Définitivement',
    removeSubcategory: 'Supprimer la Sous-catégorie',
    removeProduct: 'Supprimer le Produit',

    // Removed Items Sections
    removedSubcategories: 'Sous-catégories Supprimées',
    removedProducts: 'Produits Supprimés',
    removedSubcategoriesNote: 'Les sous-catégories supprimées sont masquées de la barre latérale. Cliquez sur le bouton + pour les restaurer ou sur la corbeille pour les supprimer définitivement.',
    removedProductsNote: 'Les produits supprimés sont masqués de la barre latérale. Cliquez sur le bouton + pour les restaurer ou sur la corbeille pour les supprimer définitivement.',
    restoreSubcategory: 'Restaurer la Sous-catégorie',
    restoreProduct: 'Restaurer le Produit',
    permanentlyDeleteSubcategory: 'Supprimer Définitivement la Sous-catégorie',
    permanentlyDeleteProduct: 'Supprimer Définitivement le Produit',

    // Product Creation Messages
    productAddedSuccessfully: 'Produit "{name}" ajouté avec succès !',
    newCategoryCreated: 'Nouvelle catégorie "{category}" créée.',
    newSubcategoryAdded: 'Nouvelle sous-catégorie "{subcategory}" ajoutée.',
    productAvailableInSidebar: 'Le produit est maintenant disponible dans la barre latérale sous {category} → {subcategory}.',
    errorAddingProduct: 'Erreur lors de l\'ajout du produit. Veuillez réessayer.',

    // Product Editing
    editProduct: 'Modifier le Produit',
    productDiameter: 'Diamètre/Taille',
    productMaterial: 'Matériau',
    productCategory: 'Catégorie',
    productSubcategory: 'Sous-catégorie',
    enterDiameter: 'ex: 1/2 pouce, 3/4 pouce, 25mm',
    enterMaterial: 'ex: PVC, Cuivre, Acier, Laiton',
    selectCategory: 'Sélectionner une catégorie',
    selectSubcategory: 'Sélectionner une sous-catégorie',
    selectCategoryFirst: 'Veuillez d\'abord sélectionner une catégorie',
    diameterRequired: 'Le diamètre/taille est requis',
    materialRequired: 'Le matériau est requis',
    categoryRequired: 'La catégorie est requise',
    subcategoryRequired: 'La sous-catégorie est requise',
    productUpdatedSuccessfully: 'Produit Mis à Jour avec Succès',
    productCustomizedSuccessfully: 'Produit Personnalisé avec Succès',
    productMovedToCategory: 'Produit déplacé vers',
    errorSavingProduct: 'Erreur lors de la Sauvegarde du Produit. Veuillez Réessayer.',

    // Export Messages
    exportSuccess: 'Exportation réussie',
    exportError: 'Échec de l\'exportation. Veuillez réessayer.',

    // Selection Mode
    'Select Export Area': 'Sélectionner la Zone d\'Exportation',
    'Selection Ready': 'Sélection Prête',
    'Confirm to export selected area': 'Confirmer pour exporter la zone sélectionnée',
    'Click and drag to select an area': 'Cliquez et faites glisser pour sélectionner une zone',
    'Confirm': 'Confirmer',
    'Clear': 'Effacer',
    'Cancel Selection': 'Annuler la Sélection',

    // Language
    language: 'Langue',
    english: 'English',
    french: 'Français',

    // Settings
    settings: 'Paramètres',
    dataStore: 'Magasin de Données',
    categoryManagement: 'Gestion des Catégories',
    hierarchicalCategoryManagement: 'Gestion Hiérarchique des Catégories',
    productHierarchy: 'Hiérarchie des Produits',
    subcategories: 'sous-catégories',
    products: 'produits',
    searchProductsCategories: 'Rechercher des produits, catégories ou sous-catégories...',
    clearSearch: 'Effacer la recherche',
    searchResults: 'Résultats de recherche',
    dataManagement: 'Gestion des Données',
    applicationSettings: 'Paramètres de l\'Application',
    sessionManagement: 'Gestion de Session',
    categoryVisibility: 'Visibilité des Catégories',
    activeCategories: 'Catégories Actives',
    removedCategories: 'Catégories Supprimées',
    removedSubcategories: 'Sous-catégories Supprimées',
    removedProducts: 'Produits Supprimés',
    hideCategory: 'Masquer la Catégorie',
    showCategory: 'Afficher la Catégorie',
    removeCategory: 'Supprimer la Catégorie',
    restoreCategory: 'Restaurer la Catégorie',
    permanentlyDeleteCategory: 'Supprimer Définitivement la Catégorie',
    removeSubcategory: 'Supprimer la Sous-catégorie',
    restoreSubcategory: 'Restaurer la Sous-catégorie',
    permanentlyDeleteSubcategory: 'Supprimer Définitivement la Sous-catégorie',
    removeProduct: 'Supprimer le Produit',
    restoreProduct: 'Restaurer le Produit',
    permanentlyDeleteProduct: 'Supprimer Définitivement le Produit',
    removedCategoriesNote: 'Les catégories supprimées sont masquées de la barre latérale. Cliquez sur le bouton + pour les restaurer ou sur la corbeille pour les supprimer définitivement.',
    resetToDefault: 'Réinitialiser par Défaut',
    saveCurrentProject: 'Sauvegarder le Projet Actuel',
    enterProjectName: 'Entrez le nom du projet...',
    save: 'Sauvegarder',
    savedProjects: 'Projets Sauvegardés',
    noSavedProjects: 'Aucun projet sauvegardé',
    loadProject: 'Charger le Projet',
    deleteProject: 'Supprimer le Projet',
    importData: 'Importer des Données',
    exportData: 'Exporter des Données',
    importFile: 'Importer un Fichier',
    exportBackup: 'Exporter une Sauvegarde',
    exportToExcel: 'Exporter vers Excel',
    importFromExcel: 'Importer depuis Excel',
    exportTemplate: 'Exporter le Modèle',
    excelExportSuccess: 'Fichier Excel exporté avec succès',
    excelImportSuccess: 'Produits importés avec succès',
    excelImportError: 'Erreur lors de l\'importation du fichier Excel',
    templateExportSuccess: 'Modèle exporté avec succès',
    templateDescription: 'Téléchargez un modèle Excel avec des exemples de produits pour vous aider à formater correctement vos données d\'importation.',
    imageUrlOrBase64: 'Image (URL ou base64)',
    processingImages: 'Traitement des images...',
    invalidImageUrl: 'URL d\'image invalide',
    invalidImageData: 'Données d\'image invalides',
    importedWithImages: 'importés avec images',
    importedWithoutImages: 'importés sans images',
    productTemplateBuilder: 'Constructeur de Modèle de Produits',
    buildTemplate: 'Construire le Modèle',
    addProduct: 'Ajouter un Produit',
    editProduct: 'Modifier le Produit',
    deleteProduct: 'Supprimer le Produit',
    clearAll: 'Tout Effacer',
    uploadImage: 'Télécharger une Image',
    imagePreview: 'Aperçu de l\'Image',
    removeImage: 'Supprimer l\'Image',
    productName: 'Nom du Produit',
    selectCategory: 'Sélectionner une Catégorie',
    selectSubcategory: 'Sélectionner une Sous-catégorie',
    enterPrice: 'Entrer le Prix',
    enterSize: 'Entrer la Taille/Diamètre',
    enterMaterial: 'Entrer le Matériau',
    requiredField: 'Champ requis',
    optionalField: 'Champ optionnel',
    productsInTemplate: 'Produits dans le Modèle',
    noProductsAdded: 'Aucun produit ajouté au modèle pour le moment',
    addFirstProduct: 'Ajoutez votre premier produit en utilisant le formulaire ci-dessus',
    duplicateProduct: 'Un produit avec ce nom existe déjà',
    templateBuilderDescription: 'Créez des modèles de produits personnalisés avec des images pour une importation Excel facile',
    processingImage: 'Traitement de l\'image...',
    imageProcessed: 'Image traitée avec succès',
    imageTooLarge: 'Le fichier image est trop volumineux (max 2MB)',
    unsupportedImageFormat: 'Format d\'image non supporté',
    exportTemplateWithProducts: 'Exporter le Modèle avec les Produits',
    excelImportSuccess: 'Produits importés avec succès',
    excelImportError: 'Erreur lors de l\'importation du fichier Excel',
    fileValidationError: 'Erreur de validation du fichier',
    processingExcelFile: 'Traitement du fichier Excel...',
    noValidProductsFound: 'Aucun produit valide trouvé',
    invalidExcelFormat: 'Format de fichier Excel invalide',
    fileReadError: 'Échec de la lecture du fichier',
    categoryCreationError: 'Échec de la création de la catégorie',
    productCreationError: 'Échec de la création du produit',
    dangerZone: 'Zone de Danger',
    clearAllDataWarning: 'Ceci supprimera définitivement tous les projets sauvegardés et réinitialisera l\'espace de travail.',
    confirmClearAllData: 'Êtes-vous sûr de vouloir effacer toutes les données ? Cette action ne peut pas être annulée.',
    clearAllData: 'Effacer Toutes les Données',
    clearAllDataTitle: 'Effacer Toutes les Données de l\'Application',
    clearAllDataDescription: 'Cette action supprimera définitivement TOUTES les données y compris les produits et catégories par défaut, laissant l\'application complètement vide.',
    dataToBeCleared: 'Les données suivantes seront effacées :',
    savedProjects: 'Projets Sauvegardés',
    customProducts: 'Produits Personnalisés',
    customCategories: 'Catégories Personnalisées',
    'Default Products': 'Produits par Défaut',
    'Default Categories': 'Catégories par Défaut',
    modifiedSettings: 'Paramètres Modifiés',
    noDataToShow: 'Aucune donnée à afficher',
    dataCleared: 'Toutes les données ont été effacées avec succès, y compris les produits et catégories par défaut',
    dataClearFailed: 'Échec de l\'effacement des données',
    restoreDefaults: 'Restaurer les Données par Défaut',
    restoreDefaultsDescription: 'Restaurer les catégories et produits par défaut',
    defaultDataRestored: 'Les données par défaut ont été restaurées',
    restoreDefaultsFailed: 'Échec de la restauration des données par défaut',
    undoAvailable: 'Vous pouvez restaurer les données par défaut en utilisant le bouton "Restaurer les Données par Défaut"',
    'Clearing...': 'Effacement...',
    // Product editing translations
    'Edit Product': 'Modifier le Produit',
    'Product name is required': 'Le nom du produit est requis',
    'Category is required': 'La catégorie est requise',
    'Subcategory is required': 'La sous-catégorie est requise',
    'Price must be a valid number': 'Le prix doit être un nombre valide',
    'Failed to update product': 'Échec de la mise à jour du produit',
    'Product Name': 'Nom du Produit',
    'Category': 'Catégorie',
    'Subcategory': 'Sous-catégorie',
    'Price': 'Prix',
    'Size/Diameter': 'Taille/Diamètre',
    'Material': 'Matériau',
    'Product Image URL': 'URL de l\'Image du Produit',
    'Save Changes': 'Enregistrer les Modifications',
    'Updating...': 'Mise à jour...',
    'Edit': 'Modifier',
    'Enter product name': 'Entrez le nom du produit',
    'Select category': 'Sélectionnez une catégorie',
    'Select subcategory': 'Sélectionnez une sous-catégorie',
    'e.g., 1/2", 3/4", 1"': 'ex: 1/2", 3/4", 1"',
    'e.g., Copper, PVC, Steel': 'ex: Cuivre, PVC, Acier',
    languageSettings: 'Paramètres de Langue',
    canvasSettings: 'Paramètres du Canevas',
    showGrid: 'Afficher la Grille',
    autoSaveSettings: 'Paramètres de Sauvegarde Automatique',
    enableAutoSave: 'Activer la Sauvegarde Automatique',
    autoSaveInterval: 'Intervalle de Sauvegarde Automatique (secondes)',
    resetSettings: 'Réinitialiser les Paramètres',
    resetSettingsWarning: 'Ceci réinitialisera tous les paramètres de l\'application à leurs valeurs par défaut.',
    confirmResetSettings: 'Êtes-vous sûr de vouloir réinitialiser tous les paramètres ?',
    resetToDefaults: 'Réinitialiser aux Valeurs par Défaut',
    exitBehavior: 'Comportement de Sortie',
    confirmBeforeExit: 'Confirmer avant de quitter',
    saveOnExit: 'Sauvegarde automatique à la sortie',
    currentSession: 'Session Actuelle',
    productsOnCanvas: 'Produits sur le canevas',
    connections: 'Connexions',
    lastSaved: 'Dernière sauvegarde',
    never: 'Jamais',
    sessionActions: 'Actions de Session',
    saveSession: 'Sauvegarder la Session',
    saveAndExit: 'Sauvegarder et Quitter',
    confirmDelete: 'Confirmer la Suppression',
    confirmDeleteProject: 'Êtes-vous sûr de vouloir supprimer ce projet ? Cette action ne peut pas être annulée.',
    confirmRemoveCategory: 'Confirmer la Suppression de Catégorie',
    confirmRemoveCategoryMessage: 'Êtes-vous sûr de vouloir supprimer cette catégorie de la barre latérale ? Vous pourrez la restaurer plus tard depuis la section des catégories supprimées.',
    confirmPermanentDeleteCategory: 'Supprimer Définitivement la Catégorie',
    confirmPermanentDeleteCategoryMessage: 'Êtes-vous sûr de vouloir supprimer définitivement cette catégorie ? Cette action ne peut pas être annulée et tous les produits de cette catégorie seront définitivement supprimés de l\'application.',
    confirmRemoveSubcategory: 'Confirmer la Suppression de Sous-catégorie',
    confirmRemoveSubcategoryMessage: 'Êtes-vous sûr de vouloir supprimer cette sous-catégorie ? Vous pourrez la restaurer plus tard depuis la section des sous-catégories supprimées.',
    confirmPermanentDeleteSubcategory: 'Supprimer Définitivement la Sous-catégorie',
    confirmPermanentDeleteSubcategoryMessage: 'Êtes-vous sûr de vouloir supprimer définitivement cette sous-catégorie ? Cette action ne peut pas être annulée et tous les produits de cette sous-catégorie seront définitivement supprimés.',
    confirmRemoveProduct: 'Confirmer la Suppression de Produit',
    confirmRemoveProductMessage: 'Êtes-vous sûr de vouloir supprimer ce produit ? Vous pourrez le restaurer plus tard depuis la section des produits supprimés.',
    confirmPermanentDeleteProduct: 'Supprimer Définitivement le Produit',
    confirmPermanentDeleteProductMessage: 'Êtes-vous sûr de vouloir supprimer définitivement ce produit ? Cette action ne peut pas être annulée et le produit sera complètement supprimé de l\'application.',
    permanentDeleteWarning: 'ATTENTION : Cette action est irréversible !',
    permanentlyDelete: 'Supprimer Définitivement',
    delete: 'Supprimer',
    projectNameRequired: 'Le nom du projet est requis',
    projectSaved: 'Projet sauvegardé avec succès',
    sessionSaved: 'Session sauvegardée avec succès',
    confirmExitSession: 'Sauvegarder la session actuelle et quitter ?',

    // Product Template Builder Integration
    'Product added to template and main catalog': 'Produit ajouté au modèle et au catalogue principal',
    'Product added to template': 'Produit ajouté au modèle',
    'already exists in main catalog': 'existe déjà dans le catalogue principal',
    'Product updated in template': 'Produit mis à jour dans le modèle',
    'Error adding product to main catalog': 'Erreur lors de l\'ajout du produit au catalogue principal',
    'In Main Catalog': 'Dans le Catalogue Principal',
    'Already Exists': 'Existe Déjà',
    'Template Summary': 'Résumé du Modèle',
    'Total Products': 'Total des Produits',
    'Products with Images': 'Produits avec Images',
    'Categories': 'Catégories',
    'Average Price': 'Prix Moyen',
    'Ready for Canvas': 'Prêt pour le Canevas',
    'With Image': 'Avec Image'
  }
};

// Category translations
export const categoryTranslations = {
  en: {
    'Appliance Supplies & Parts': 'Appliance Supplies & Parts',
    'Bath Waste & Overflows': 'Bath Waste & Overflows',
    'Couplings & Gaskets': 'Couplings & Gaskets',
    'Drain Openers': 'Drain Openers',
    'Drainage Products': 'Drainage Products',
    'Fittings & Valves': 'Fittings & Valves',
    'Gas Supplies & Fittings': 'Gas Supplies & Fittings',
    'Job Site Essentials': 'Job Site Essentials',
    'Kitchen & Bath': 'Kitchen & Bath',
    'PEX Plumbing System': 'PEX Plumbing System',
    'Pipe Hangers, Straps & Hooks': 'Pipe Hangers, Straps & Hooks',
    'Pipe Insulation': 'Pipe Insulation',
    'Plumbing': 'Plumbing',
    'Repair & Testing Parts': 'Repair & Testing Parts',
    'Stub Outs': 'Stub Outs',
    'Toilet Seats': 'Toilet Seats',
    'Tools': 'Tools',
    'Tubular': 'Tubular'
  },
  fr: {
    'Appliance Supplies & Parts': 'Fournitures et Pièces d\'Appareils',
    'Bath Waste & Overflows': 'Évacuations et Trop-pleins de Bain',
    'Couplings & Gaskets': 'Raccords et Joints',
    'Drain Openers': 'Débouchoirs',
    'Drainage Products': 'Produits de Drainage',
    'Fittings & Valves': 'Raccords et Vannes',
    'Gas Supplies & Fittings': 'Fournitures et Raccords de Gaz',
    'Job Site Essentials': 'Essentiels de Chantier',
    'Kitchen & Bath': 'Cuisine et Salle de Bain',
    'PEX Plumbing System': 'Système de Plomberie PEX',
    'Pipe Hangers, Straps & Hooks': 'Supports, Sangles et Crochets de Tuyaux',
    'Pipe Insulation': 'Isolation de Tuyaux',
    'Plumbing': 'Plomberie',
    'Repair & Testing Parts': 'Pièces de Réparation et de Test',
    'Stub Outs': 'Sorties de Tuyaux',
    'Toilet Seats': 'Sièges de Toilette',
    'Tools': 'Outils',
    'Tubular': 'Tubulaire'
  }
};

export const getTranslation = (key, language = 'en') => {
  const keys = key.split('.');
  let value = translations[language];

  for (const k of keys) {
    value = value?.[k];
  }

  return value || key;
};

export const getCategoryTranslation = (category, language = 'en') => {
  return categoryTranslations[language]?.[category] || category;
};
