/**
 * Enhanced Storage Manager for PlomDesign
 * Handles persistent storage information management with dedicated directory structure
 */

class EnhancedStorageManager {
  constructor() {
    this.storageDirectoryName = 'PlomDesign';
    this.metadataFileName = 'storage-metadata.json';
    this.workspaceFileName = 'workspace-data.json';
    this.backupPrefix = 'backup-';
    this.isFileSystemSupported = this.checkFileSystemSupport();
    this.fallbackToLocalStorage = !this.isFileSystemSupported;
    
    // Initialize storage directory on construction
    this.initializeStorageDirectory();
  }

  /**
   * Check if File System Access API is supported
   */
  checkFileSystemSupport() {
    return 'showDirectoryPicker' in window && 'showSaveFilePicker' in window;
  }

  /**
   * Initialize storage directory and handle permissions
   */
  async initializeStorageDirectory() {
    if (this.fallbackToLocalStorage) {
      console.log('File System Access API not supported, using localStorage fallback');
      return;
    }

    try {
      // Check if we have a stored directory handle
      const storedHandle = await this.getStoredDirectoryHandle();
      if (storedHandle) {
        this.directoryHandle = storedHandle;
        console.log('Using existing PlomDesign directory');
      } else {
        console.log('No existing directory found, will prompt user when needed');
      }
    } catch (error) {
      console.warn('Error initializing storage directory:', error);
      this.fallbackToLocalStorage = true;
    }
  }

  /**
   * Get stored directory handle from IndexedDB
   */
  async getStoredDirectoryHandle() {
    try {
      const db = await this.openIndexedDB();
      const transaction = db.transaction(['directoryHandles'], 'readonly');
      const store = transaction.objectStore('directoryHandles');
      const request = store.get('plomDesignDirectory');
      
      return new Promise((resolve, reject) => {
        request.onsuccess = () => {
          resolve(request.result?.handle);
        };
        request.onerror = () => {
          reject(request.error);
        };
      });
    } catch (error) {
      console.warn('Error getting stored directory handle:', error);
      return null;
    }
  }

  /**
   * Store directory handle in IndexedDB
   */
  async storeDirectoryHandle(handle) {
    try {
      const db = await this.openIndexedDB();
      const transaction = db.transaction(['directoryHandles'], 'readwrite');
      const store = transaction.objectStore('directoryHandles');
      await store.put({ id: 'plomDesignDirectory', handle });
      console.log('Directory handle stored successfully');
    } catch (error) {
      console.warn('Error storing directory handle:', error);
    }
  }

  /**
   * Open IndexedDB for storing directory handles
   */
  async openIndexedDB() {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open('PlomDesignStorage', 1);
      
      request.onupgradeneeded = (event) => {
        const db = event.target.result;
        if (!db.objectStoreNames.contains('directoryHandles')) {
          db.createObjectStore('directoryHandles', { keyPath: 'id' });
        }
      };
      
      request.onsuccess = () => {
        resolve(request.result);
      };
      
      request.onerror = () => {
        reject(request.error);
      };
    });
  }

  /**
   * Prompt user to select or create PlomDesign directory
   */
  async promptForDirectory() {
    if (this.fallbackToLocalStorage) {
      throw new Error('File system access not supported');
    }

    try {
      // Try to get existing directory or create new one
      const directoryHandle = await window.showDirectoryPicker({
        mode: 'readwrite',
        startIn: 'documents'
      });

      // Check if this is the PlomDesign directory or create it
      let plomDesignHandle;
      try {
        plomDesignHandle = await directoryHandle.getDirectoryHandle(this.storageDirectoryName);
      } catch (error) {
        // Directory doesn't exist, create it
        plomDesignHandle = await directoryHandle.getDirectoryHandle(this.storageDirectoryName, { create: true });
      }

      this.directoryHandle = plomDesignHandle;
      await this.storeDirectoryHandle(plomDesignHandle);
      
      return {
        success: true,
        message: `PlomDesign directory created/accessed successfully`,
        path: directoryHandle.name + '/' + this.storageDirectoryName
      };
    } catch (error) {
      if (error.name === 'AbortError') {
        return {
          success: false,
          message: 'Directory selection cancelled by user'
        };
      }
      throw error;
    }
  }

  /**
   * Save storage metadata to dedicated directory
   */
  async saveStorageMetadata(metadata) {
    if (this.fallbackToLocalStorage) {
      return this.saveToLocalStorage('storage-metadata', metadata);
    }

    try {
      if (!this.directoryHandle) {
        const result = await this.promptForDirectory();
        if (!result.success) {
          return result;
        }
      }

      const fileHandle = await this.directoryHandle.getFileHandle(this.metadataFileName, { create: true });
      const writable = await fileHandle.createWritable();
      
      const metadataWithTimestamp = {
        ...metadata,
        savedAt: new Date().toISOString(),
        version: '1.0.0'
      };
      
      await writable.write(JSON.stringify(metadataWithTimestamp, null, 2));
      await writable.close();

      return {
        success: true,
        message: 'Storage metadata saved successfully',
        location: 'file-system'
      };
    } catch (error) {
      console.warn('Error saving storage metadata to file system:', error);
      // Fallback to localStorage
      return this.saveToLocalStorage('storage-metadata', metadata);
    }
  }

  /**
   * Load storage metadata from dedicated directory
   */
  async loadStorageMetadata() {
    if (this.fallbackToLocalStorage) {
      return this.loadFromLocalStorage('storage-metadata');
    }

    try {
      if (!this.directoryHandle) {
        const storedHandle = await this.getStoredDirectoryHandle();
        if (!storedHandle) {
          return this.loadFromLocalStorage('storage-metadata');
        }
        this.directoryHandle = storedHandle;
      }

      const fileHandle = await this.directoryHandle.getFileHandle(this.metadataFileName);
      const file = await fileHandle.getFile();
      const content = await file.text();
      const metadata = JSON.parse(content);

      return {
        success: true,
        data: metadata,
        location: 'file-system',
        lastModified: file.lastModified
      };
    } catch (error) {
      console.warn('Error loading storage metadata from file system:', error);
      // Fallback to localStorage
      return this.loadFromLocalStorage('storage-metadata');
    }
  }

  /**
   * Save workspace data to dedicated directory
   */
  async saveWorkspaceData(workspaceData) {
    if (this.fallbackToLocalStorage) {
      return this.saveToLocalStorage('workspace-data', workspaceData);
    }

    try {
      if (!this.directoryHandle) {
        const result = await this.promptForDirectory();
        if (!result.success) {
          return result;
        }
      }

      // Save main workspace file
      const fileHandle = await this.directoryHandle.getFileHandle(this.workspaceFileName, { create: true });
      const writable = await fileHandle.createWritable();
      
      const workspaceWithTimestamp = {
        ...workspaceData,
        savedAt: new Date().toISOString(),
        version: '1.0.0'
      };
      
      await writable.write(JSON.stringify(workspaceWithTimestamp, null, 2));
      await writable.close();

      // Create backup copy
      await this.createBackupCopy(workspaceWithTimestamp);

      return {
        success: true,
        message: 'Workspace data saved successfully',
        location: 'file-system'
      };
    } catch (error) {
      console.warn('Error saving workspace data to file system:', error);
      // Fallback to localStorage
      return this.saveToLocalStorage('workspace-data', workspaceData);
    }
  }

  /**
   * Load workspace data from dedicated directory
   */
  async loadWorkspaceData() {
    if (this.fallbackToLocalStorage) {
      return this.loadFromLocalStorage('workspace-data');
    }

    try {
      if (!this.directoryHandle) {
        const storedHandle = await this.getStoredDirectoryHandle();
        if (!storedHandle) {
          return this.loadFromLocalStorage('workspace-data');
        }
        this.directoryHandle = storedHandle;
      }

      const fileHandle = await this.directoryHandle.getFileHandle(this.workspaceFileName);
      const file = await fileHandle.getFile();
      const content = await file.text();
      const workspaceData = JSON.parse(content);

      return {
        success: true,
        data: workspaceData,
        location: 'file-system',
        lastModified: file.lastModified
      };
    } catch (error) {
      console.warn('Error loading workspace data from file system:', error);
      // Fallback to localStorage
      return this.loadFromLocalStorage('workspace-data');
    }
  }

  /**
   * Create backup copy of workspace data
   */
  async createBackupCopy(workspaceData) {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const backupFileName = `${this.backupPrefix}${timestamp}.json`;
      
      const backupHandle = await this.directoryHandle.getFileHandle(backupFileName, { create: true });
      const writable = await backupHandle.createWritable();
      
      await writable.write(JSON.stringify(workspaceData, null, 2));
      await writable.close();

      // Clean old backups (keep only last 5)
      await this.cleanOldBackups();
      
      console.log(`Backup created: ${backupFileName}`);
    } catch (error) {
      console.warn('Error creating backup copy:', error);
    }
  }

  /**
   * Clean old backup files
   */
  async cleanOldBackups() {
    try {
      const backupFiles = [];
      
      for await (const [name, handle] of this.directoryHandle.entries()) {
        if (name.startsWith(this.backupPrefix) && name.endsWith('.json')) {
          const file = await handle.getFile();
          backupFiles.push({
            name,
            handle,
            lastModified: file.lastModified
          });
        }
      }

      // Sort by last modified (newest first) and keep only 5
      backupFiles.sort((a, b) => b.lastModified - a.lastModified);
      
      if (backupFiles.length > 5) {
        const filesToDelete = backupFiles.slice(5);
        for (const file of filesToDelete) {
          await this.directoryHandle.removeEntry(file.name);
          console.log(`Removed old backup: ${file.name}`);
        }
      }
    } catch (error) {
      console.warn('Error cleaning old backups:', error);
    }
  }

  /**
   * Fallback: Save to localStorage
   */
  saveToLocalStorage(key, data) {
    try {
      const dataWithTimestamp = {
        ...data,
        savedAt: new Date().toISOString(),
        version: '1.0.0'
      };
      
      localStorage.setItem(`plom-enhanced-${key}`, JSON.stringify(dataWithTimestamp));
      
      return {
        success: true,
        message: 'Data saved to browser storage',
        location: 'localStorage'
      };
    } catch (error) {
      return {
        success: false,
        message: 'Failed to save to browser storage: ' + error.message,
        location: 'localStorage'
      };
    }
  }

  /**
   * Fallback: Load from localStorage
   */
  loadFromLocalStorage(key) {
    try {
      const stored = localStorage.getItem(`plom-enhanced-${key}`);
      if (!stored) {
        return {
          success: false,
          message: 'No data found in browser storage',
          location: 'localStorage'
        };
      }

      const data = JSON.parse(stored);
      return {
        success: true,
        data,
        location: 'localStorage',
        lastModified: new Date(data.savedAt).getTime()
      };
    } catch (error) {
      return {
        success: false,
        message: 'Failed to load from browser storage: ' + error.message,
        location: 'localStorage'
      };
    }
  }

  /**
   * Get storage status and information
   */
  async getStorageStatus() {
    const status = {
      fileSystemSupported: this.isFileSystemSupported,
      currentMode: this.fallbackToLocalStorage ? 'localStorage' : 'file-system',
      directoryConfigured: !!this.directoryHandle,
      storageLocation: null
    };

    if (!this.fallbackToLocalStorage && this.directoryHandle) {
      try {
        // Try to get directory path information
        status.storageLocation = 'PlomDesign directory (file system)';
        status.directoryAccessible = true;
      } catch (error) {
        status.directoryAccessible = false;
        status.error = error.message;
      }
    } else {
      status.storageLocation = 'Browser localStorage';
    }

    return status;
  }

  /**
   * Export data with enhanced storage metadata
   */
  async exportWithStorageMetadata(exportData, storageInfo) {
    const enhancedExportData = {
      ...exportData,
      storageMetadata: {
        ...storageInfo,
        exportedAt: new Date().toISOString(),
        exportedFrom: this.fallbackToLocalStorage ? 'localStorage' : 'file-system',
        version: '1.0.0'
      }
    };

    // Save storage metadata to dedicated directory
    await this.saveStorageMetadata(storageInfo);
    
    // Save workspace data to dedicated directory
    await this.saveWorkspaceData(exportData);

    return enhancedExportData;
  }

  /**
   * Auto-restore on app startup
   */
  async autoRestoreOnStartup() {
    try {
      console.log('Attempting auto-restore on startup...');
      
      // Try to load workspace data
      const workspaceResult = await this.loadWorkspaceData();
      const metadataResult = await this.loadStorageMetadata();

      if (workspaceResult.success || metadataResult.success) {
        return {
          success: true,
          workspaceData: workspaceResult.success ? workspaceResult.data : null,
          storageMetadata: metadataResult.success ? metadataResult.data : null,
          location: workspaceResult.location || metadataResult.location,
          message: `Auto-restored data from ${workspaceResult.location || metadataResult.location}`
        };
      } else {
        return {
          success: false,
          message: 'No stored data found for auto-restore'
        };
      }
    } catch (error) {
      console.warn('Error during auto-restore:', error);
      return {
        success: false,
        message: 'Auto-restore failed: ' + error.message
      };
    }
  }
}

// Create singleton instance
const enhancedStorageManager = new EnhancedStorageManager();

export default enhancedStorageManager;
