import React from 'react';
import { Loader2, Database, CheckCircle, AlertCircle } from 'lucide-react';

const DataLoadingIndicator = ({ 
  isLoading, 
  message, 
  progress = null,
  type = 'loading' // 'loading', 'success', 'error'
}) => {
  if (!isLoading && type === 'loading') return null;

  const getIcon = () => {
    switch (type) {
      case 'success':
        return <CheckCircle className="w-8 h-8 text-green-500" />;
      case 'error':
        return <AlertCircle className="w-8 h-8 text-red-500" />;
      default:
        return <Loader2 className="w-8 h-8 text-primary-500 animate-spin" />;
    }
  };

  const getBackgroundColor = () => {
    switch (type) {
      case 'success':
        return 'bg-green-500/10 border-green-500/20';
      case 'error':
        return 'bg-red-500/10 border-red-500/20';
      default:
        return 'bg-primary-500/10 border-primary-500/20';
    }
  };

  return (
    <div className="fixed inset-0 bg-dark-900/80 backdrop-blur-sm z-[9999] flex items-center justify-center">
      <div className={`${getBackgroundColor()} border rounded-xl p-8 max-w-md w-full mx-4 shadow-2xl`}>
        <div className="flex flex-col items-center text-center space-y-4">
          {/* Icon */}
          <div className="relative">
            {getIcon()}
            {type === 'loading' && (
              <div className="absolute inset-0 rounded-full border-2 border-primary-500/20 animate-pulse"></div>
            )}
          </div>

          {/* Title */}
          <div className="space-y-2">
            <h3 className="text-xl font-semibold text-white">
              {type === 'loading' && 'Loading Your Workspace'}
              {type === 'success' && 'Data Loaded Successfully'}
              {type === 'error' && 'Loading Error'}
            </h3>
            
            {/* Message */}
            <p className="text-dark-300 text-sm">
              {message || 'Please wait while we restore your previous work...'}
            </p>
          </div>

          {/* Progress Bar */}
          {progress !== null && type === 'loading' && (
            <div className="w-full space-y-2">
              <div className="w-full bg-dark-700 rounded-full h-2">
                <div 
                  className="bg-primary-500 h-2 rounded-full transition-all duration-300 ease-out"
                  style={{ width: `${Math.min(100, Math.max(0, progress))}%` }}
                ></div>
              </div>
              <p className="text-xs text-dark-400">
                {Math.round(progress)}% complete
              </p>
            </div>
          )}

          {/* Loading Animation */}
          {type === 'loading' && (
            <div className="flex space-x-1">
              <div className="w-2 h-2 bg-primary-500 rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
              <div className="w-2 h-2 bg-primary-500 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
              <div className="w-2 h-2 bg-primary-500 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
            </div>
          )}

          {/* Additional Info */}
          {type === 'loading' && (
            <div className="text-xs text-dark-400 space-y-1">
              <div className="flex items-center justify-center space-x-2">
                <Database className="w-3 h-3" />
                <span>Restoring canvas layout and connections</span>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default DataLoadingIndicator;
