import React, { useState, useEffect } from 'react';
import { X, FileText, Package, Hash, Edit3, Check, AlertCircle, DollarSign, Calculator, Edit2 } from 'lucide-react';
import { useLanguage } from '../contexts/LanguageContext';

const InvoicePreviewModal = ({
  isOpen,
  onClose,
  invoiceData,
  onGeneratePDF
}) => {
  const { t } = useLanguage();

  // State for editable products
  const [editableProducts, setEditableProducts] = useState([]);
  const [editingField, setEditingField] = useState(null); // { productIndex, field }
  const [tempValue, setTempValue] = useState('');
  const [validationError, setValidationError] = useState('');

  // State for editable titles
  const [reportTitle, setReportTitle] = useState('Plumbing Installation Report');
  const [reportSubtitle, setReportSubtitle] = useState('Professional Installation Summary');
  const [editingTitle, setEditingTitle] = useState(false);
  const [editingSubtitle, setEditingSubtitle] = useState(false);

  // Initialize editable products when invoice data changes
  useEffect(() => {
    if (invoiceData?.aggregatedProducts) {
      setEditableProducts(invoiceData.aggregatedProducts.map(product => ({
        ...product,
        quantity: product.quantity,
        price: product.price || 0,
        lineTotal: (product.quantity || 0) * (product.price || 0)
      })));
    }
  }, [invoiceData]);

  if (!isOpen || !invoiceData) return null;

  const { summary } = invoiceData;

  // Calculate real-time totals from editable products (with pricing restored)
  const calculateTotals = () => {
    const totalItems = editableProducts.reduce((sum, product) => sum + product.quantity, 0);
    const grandTotal = editableProducts.reduce((sum, product) => sum + product.lineTotal, 0);
    return {
      totalUniqueProducts: editableProducts.length,
      totalItemsRequired: totalItems,
      grandTotal: grandTotal
    };
  };

  const currentTotals = calculateTotals();

  // Validation functions
  const validateQuantity = (value) => {
    const num = parseInt(value);
    if (isNaN(num) || num <= 0 || !Number.isInteger(num)) {
      return 'Quantity must be a positive integer';
    }
    return null;
  };

  const validatePrice = (value) => {
    const num = parseFloat(value);
    if (isNaN(num) || num < 0) {
      return 'Price must be a valid number (0 or greater)';
    }
    return null;
  };

  // Enhanced editing functions with pricing support
  const startEditing = (productIndex, field) => {
    const product = editableProducts[productIndex];
    setEditingField({ productIndex, field });
    if (field === 'quantity') {
      setTempValue(product.quantity.toString());
    } else if (field === 'price') {
      setTempValue(product.price.toString());
    }
    setValidationError('');
  };

  const cancelEditing = () => {
    setEditingField(null);
    setTempValue('');
    setValidationError('');
  };

  const saveEdit = () => {
    if (!editingField) return;

    const { productIndex, field } = editingField;
    let error = null;
    let newValue = null;

    if (field === 'quantity') {
      error = validateQuantity(tempValue);
      newValue = parseInt(tempValue);
    } else if (field === 'price') {
      error = validatePrice(tempValue);
      newValue = parseFloat(tempValue);
    }

    if (error) {
      setValidationError(error);
      return;
    }

    // Update the product with recalculated line total
    const updatedProducts = [...editableProducts];
    const updatedProduct = { ...updatedProducts[productIndex], [field]: newValue };

    // Recalculate line total
    updatedProduct.lineTotal = updatedProduct.quantity * updatedProduct.price;

    updatedProducts[productIndex] = updatedProduct;
    setEditableProducts(updatedProducts);
    cancelEditing();
  };

  // Title editing functions
  const startEditingTitle = () => {
    setEditingTitle(true);
  };

  const saveTitle = (newTitle) => {
    setReportTitle(newTitle.trim() || 'Plumbing Installation Report');
    setEditingTitle(false);
  };

  const startEditingSubtitle = () => {
    setEditingSubtitle(true);
  };

  const saveSubtitle = (newSubtitle) => {
    setReportSubtitle(newSubtitle.trim() || 'Professional Installation Summary');
    setEditingSubtitle(false);
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      saveEdit();
    } else if (e.key === 'Escape') {
      cancelEditing();
    }
  };

  // Enhanced PDF generation with edited values and custom titles
  const handleGeneratePDF = () => {
    // Create updated invoice data with edited values and custom titles
    const updatedInvoiceData = {
      aggregatedProducts: editableProducts,
      summary: {
        ...summary,
        ...currentTotals
      },
      customTitles: {
        title: reportTitle,
        subtitle: reportSubtitle
      }
    };

    // Pass the updated data to the PDF generation function
    onGeneratePDF(updatedInvoiceData);
  };



  return (
    <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50">
      <div className="bg-dark-800 rounded-lg shadow-xl w-full max-w-5xl mx-4 max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-dark-700">
          <div className="flex items-center gap-3">
            <FileText className="w-6 h-6 text-primary-500" />
            <div>
              <h2 className="text-xl font-bold text-white">Report Preview</h2>
              <p className="text-sm text-dark-300">
                {summary.invoiceNumber} • {summary.date}
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-dark-700 rounded-lg transition-colors"
          >
            <X className="w-5 h-5 text-dark-400" />
          </button>
        </div>

        {/* Editable Report Titles */}
        <div className="p-6 border-b border-dark-700 bg-dark-750">
          <div className="space-y-4">
            {/* Main Title */}
            <div>
              <label className="block text-sm font-medium text-dark-300 mb-2">Report Title</label>
              {editingTitle ? (
                <input
                  type="text"
                  defaultValue={reportTitle}
                  onBlur={(e) => saveTitle(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') saveTitle(e.target.value);
                    if (e.key === 'Escape') setEditingTitle(false);
                  }}
                  className="w-full px-4 py-2 bg-dark-600 border border-primary-500 rounded-lg text-white text-lg font-semibold focus:outline-none focus:ring-2 focus:ring-primary-500"
                  autoFocus
                />
              ) : (
                <div
                  onClick={startEditingTitle}
                  className="group flex items-center gap-2 px-4 py-2 bg-dark-600 hover:bg-dark-500 rounded-lg cursor-pointer transition-colors"
                >
                  <span className="text-lg font-semibold text-white flex-1">{reportTitle}</span>
                  <Edit2 className="w-4 h-4 text-dark-400 group-hover:text-primary-400 transition-colors" />
                </div>
              )}
            </div>

            {/* Subtitle */}
            <div>
              <label className="block text-sm font-medium text-dark-300 mb-2">Report Subtitle</label>
              {editingSubtitle ? (
                <input
                  type="text"
                  defaultValue={reportSubtitle}
                  onBlur={(e) => saveSubtitle(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') saveSubtitle(e.target.value);
                    if (e.key === 'Escape') setEditingSubtitle(false);
                  }}
                  className="w-full px-4 py-2 bg-dark-600 border border-primary-500 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500"
                  autoFocus
                />
              ) : (
                <div
                  onClick={startEditingSubtitle}
                  className="group flex items-center gap-2 px-4 py-2 bg-dark-600 hover:bg-dark-500 rounded-lg cursor-pointer transition-colors"
                >
                  <span className="text-white flex-1">{reportSubtitle}</span>
                  <Edit2 className="w-4 h-4 text-dark-400 group-hover:text-primary-400 transition-colors" />
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-300px)]">
          {/* Enhanced Summary Cards with Pricing */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="bg-dark-700 rounded-lg p-4">
              <div>
                <p className="text-base text-dark-300">Unique Products</p>
                <p className="text-2xl font-bold text-white">{currentTotals.totalUniqueProducts}</p>
              </div>
            </div>

            <div className="bg-dark-700 rounded-lg p-4">
              <div>
                <p className="text-base text-dark-300">Total Items</p>
                <p className="text-2xl font-bold text-white">{currentTotals.totalItemsRequired}</p>
              </div>
            </div>

            <div className="bg-dark-700 rounded-lg p-4">
              <div>
                <p className="text-base text-dark-300">Grand Total</p>
                <p className="text-2xl font-bold text-white">{currentTotals.grandTotal.toFixed(2)}</p>
              </div>
            </div>
          </div>

          {/* Enhanced Products Table with Pricing */}
          <div className="bg-dark-700 rounded-lg overflow-hidden">
            <div className="px-6 py-4 border-b border-dark-600">
              <h3 className="text-lg font-semibold text-white">Aggregated Product List</h3>
              <p className="text-sm text-dark-300">
                Products automatically grouped by identical specifications • Click values to edit
              </p>
              {validationError && (
                <div className="mt-2 flex items-center gap-2 text-red-400 text-sm">
                  <AlertCircle className="w-4 h-4" />
                  {validationError}
                </div>
              )}
            </div>

            <div className="overflow-x-auto">
              <table className="w-full table-fixed">
                <thead className="bg-dark-600">
                  <tr>
                    <th className="w-1/2 px-6 py-4 text-left text-xs font-semibold text-dark-100 uppercase tracking-wider border-r border-dark-500">
                      Product Information
                    </th>
                    <th className="w-1/6 px-4 py-4 text-center text-xs font-semibold text-dark-100 uppercase tracking-wider border-r border-dark-500">
                      Unit Price
                    </th>
                    <th className="w-1/6 px-4 py-4 text-center text-xs font-semibold text-dark-100 uppercase tracking-wider border-r border-dark-500">
                      Quantity
                    </th>
                    <th className="w-1/6 px-4 py-4 text-center text-xs font-semibold text-dark-100 uppercase tracking-wider">
                      Line Total
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-dark-600">
                  {editableProducts.map((product, index) => (
                    <tr key={index} className="hover:bg-dark-600/50 transition-colors">
                      <td className="px-6 py-5 border-r border-dark-500 w-1/2">
                        <div className="space-y-2 max-w-full">
                          <div className="text-sm font-semibold text-white leading-tight break-words overflow-hidden">
                            <div className="line-clamp-2 max-w-full">
                              {product.name}
                            </div>
                          </div>
                          <div className="text-xs text-dark-300 font-medium">
                            <div className="flex flex-wrap gap-1 max-w-full">
                              <span className="inline-block bg-dark-600 px-2 py-1 rounded text-xs whitespace-nowrap">
                                {product.diameter}
                              </span>
                              <span className="inline-block bg-dark-600 px-2 py-1 rounded text-xs whitespace-nowrap">
                                {product.material}
                              </span>
                              <span className="inline-block bg-dark-600 px-2 py-1 rounded text-xs whitespace-nowrap">
                                {product.category}
                              </span>
                            </div>
                          </div>
                        </div>
                      </td>

                      {/* Editable Unit Price */}
                      <td className="px-4 py-5 text-center border-r border-dark-500 w-1/6">
                        <div className="flex items-center justify-center max-w-full">
                          {editingField?.productIndex === index && editingField?.field === 'price' ? (
                            <div className="flex items-center gap-2">
                              <input
                                type="number"
                                value={tempValue}
                                onChange={(e) => setTempValue(e.target.value)}
                                onKeyDown={handleKeyPress}
                                onBlur={saveEdit}
                                className="w-20 px-2 py-1 text-center bg-dark-600 border border-primary-500 rounded text-white text-sm font-medium focus:outline-none focus:ring-1 focus:ring-primary-500"
                                min="0"
                                step="0.01"
                                autoFocus
                              />
                              <button
                                onClick={saveEdit}
                                className="p-1 text-green-400 hover:text-green-300 hover:bg-green-400/10 rounded transition-all"
                                title="Save"
                              >
                                <Check className="w-3 h-3" />
                              </button>
                            </div>
                          ) : (
                            <button
                              onClick={() => startEditing(index, 'price')}
                              className="group inline-flex items-center px-3 py-2 rounded-md text-sm font-semibold bg-yellow-600 text-white hover:bg-yellow-700 transition-all duration-200 shadow-sm hover:shadow-md min-w-[70px] max-w-full justify-center truncate"
                              title="Click to edit price"
                            >
                              <span className="text-xs opacity-70 mr-1">$</span>
                              <span className="truncate">{product.price.toFixed(2)}</span>
                              <Edit3 className="w-3 h-3 ml-1 opacity-60 group-hover:opacity-100 transition-opacity flex-shrink-0" />
                            </button>
                          )}
                        </div>
                      </td>

                      {/* Editable Quantity */}
                      <td className="px-4 py-5 text-center border-r border-dark-500 w-1/6">
                        <div className="flex items-center justify-center max-w-full">
                          {editingField?.productIndex === index && editingField?.field === 'quantity' ? (
                            <div className="flex items-center gap-2">
                              <input
                                type="number"
                                value={tempValue}
                                onChange={(e) => setTempValue(e.target.value)}
                                onKeyDown={handleKeyPress}
                                onBlur={saveEdit}
                                className="w-16 px-2 py-1 text-center bg-dark-600 border border-primary-500 rounded text-white text-sm font-medium focus:outline-none focus:ring-1 focus:ring-primary-500"
                                min="1"
                                step="1"
                                autoFocus
                              />
                              <button
                                onClick={saveEdit}
                                className="p-1 text-green-400 hover:text-green-300 hover:bg-green-400/10 rounded transition-all"
                                title="Save"
                              >
                                <Check className="w-3 h-3" />
                              </button>
                            </div>
                          ) : (
                            <button
                              onClick={() => startEditing(index, 'quantity')}
                              className="group inline-flex items-center px-3 py-2 rounded-md text-sm font-semibold bg-primary-600 text-white hover:bg-primary-700 transition-all duration-200 shadow-sm hover:shadow-md min-w-[60px] max-w-full justify-center"
                              title="Click to edit quantity"
                            >
                              <span className="truncate">{product.quantity}</span>
                              <Edit3 className="w-3 h-3 ml-1 opacity-60 group-hover:opacity-100 transition-opacity flex-shrink-0" />
                            </button>
                          )}
                        </div>
                      </td>

                      {/* Line Total */}
                      <td className="px-4 py-5 text-center w-1/6">
                        <div className="flex items-center justify-center max-w-full">
                          <div className="inline-flex items-center px-3 py-2 rounded-md bg-green-600 text-white font-semibold min-w-[80px] max-w-full justify-center text-sm">
                            <Calculator className="w-3 h-3 mr-1 opacity-70 flex-shrink-0" />
                            <span className="text-xs opacity-70 mr-1 flex-shrink-0">$</span>
                            <span className="truncate">{product.lineTotal.toFixed(2)}</span>
                          </div>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Enhanced Grand Total Section */}
          <div className="mt-6 bg-dark-700 rounded-lg p-6">
            <div className="flex justify-between items-center">
              <div className="text-lg font-semibold text-white">Report Summary</div>
              <div className="text-right">
                <div className="text-sm text-dark-300 mb-1">Grand Total</div>
                <div className="text-3xl font-bold text-green-400">
                  {currentTotals.grandTotal.toFixed(2)}
                </div>
              </div>
            </div>
            <div className="mt-4 grid grid-cols-2 gap-4 text-sm">
              <div className="flex justify-between">
                <span className="text-dark-300">Products:</span>
                <span className="text-white font-medium">{currentTotals.totalUniqueProducts}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-dark-300">Total Items:</span>
                <span className="text-white font-medium">{currentTotals.totalItemsRequired}</span>
              </div>
            </div>
          </div>

        </div>

        {/* Footer Actions */}
        <div className="flex items-center justify-between p-6 border-t border-dark-700 bg-dark-750">
          <div className="text-sm text-dark-300">
            <div>Generated from {summary.totalItemsRequired} canvas items → {currentTotals.totalUniqueProducts} product lines</div>
            <div className="text-xs text-dark-400 mt-1">
              Click prices and quantities to edit • Changes update totals in real-time
            </div>
          </div>

          <div className="flex gap-3">
            <button
              onClick={onClose}
              className="px-4 py-2 border border-dark-600 rounded-lg text-dark-200 hover:bg-dark-700 transition-colors"
            >
              Cancel
            </button>

            <button
              onClick={handleGeneratePDF}
              className="flex items-center gap-2 px-4 py-2 bg-primary-600 hover:bg-primary-700 rounded-lg text-white transition-colors"
            >
              <FileText className="w-4 h-4" />
              Generate PDF
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InvoicePreviewModal;
