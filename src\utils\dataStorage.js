/**
 * Data Storage Utility for PlomDesign
 * Handles automatic data persistence, loading, and backup functionality
 */

// Storage keys for different data types
export const STORAGE_KEYS = {
  CANVAS_STATE: 'plom_canvas_state',
  PRODUCTS: 'plom_products',
  USER_PREFERENCES: 'plom_user_preferences',
  CONNECTIONS: 'plom_connections',
  SETTINGS: 'plom_settings',
  LAST_SAVE: 'plom_last_save_timestamp'
};

// Default data structure
export const DEFAULT_DATA = {
  canvasProducts: [],
  connections: [],
  selectedProducts: [],
  settings: {
    showGrid: true,
    autoSave: true,
    autoLoad: true,
    backupInterval: 300000, // 5 minutes
    maxBackups: 10
  },
  userPreferences: {
    language: 'en',
    theme: 'dark',
    sidebarWidth: 320
  },
  metadata: {
    version: '1.0.0',
    created: null,
    lastModified: null,
    totalSessions: 0
  }
};

/**
 * Data Storage Manager Class
 */
export class DataStorageManager {
  constructor() {
    this.isSupported = this.checkStorageSupport();
    this.autoSaveInterval = null;
    this.backupInterval = null;
  }

  /**
   * Check if localStorage is supported
   */
  checkStorageSupport() {
    try {
      const test = '__storage_test__';
      localStorage.setItem(test, test);
      localStorage.removeItem(test);
      return true;
    } catch (e) {
      console.warn('localStorage not supported:', e);
      return false;
    }
  }

  /**
   * Generate timestamped filename
   */
  generateTimestamp() {
    return new Date().toISOString().replace(/[:.]/g, '-');
  }

  /**
   * Save data to localStorage with error handling
   */
  saveData(key, data) {
    if (!this.isSupported) {
      console.warn('Storage not supported, data not saved');
      return false;
    }

    try {
      const dataWithTimestamp = {
        ...data,
        metadata: {
          ...data.metadata,
          lastModified: new Date().toISOString(),
          version: DEFAULT_DATA.metadata.version
        }
      };

      localStorage.setItem(key, JSON.stringify(dataWithTimestamp));
      localStorage.setItem(STORAGE_KEYS.LAST_SAVE, new Date().toISOString());
      
      console.log(`Data saved successfully: ${key}`);
      return true;
    } catch (error) {
      console.error('Error saving data:', error);
      return false;
    }
  }

  /**
   * Load data from localStorage with error handling
   */
  loadData(key, defaultValue = null) {
    if (!this.isSupported) {
      console.warn('Storage not supported, returning default value');
      return defaultValue;
    }

    try {
      const stored = localStorage.getItem(key);
      if (!stored) {
        return defaultValue;
      }

      const parsed = JSON.parse(stored);
      
      // Validate data structure
      if (this.validateDataStructure(parsed)) {
        console.log(`Data loaded successfully: ${key}`);
        return parsed;
      } else {
        console.warn(`Invalid data structure for ${key}, using default`);
        return defaultValue;
      }
    } catch (error) {
      console.error('Error loading data:', error);
      return defaultValue;
    }
  }

  /**
   * Validate data structure
   */
  validateDataStructure(data) {
    if (!data || typeof data !== 'object') {
      console.warn('Data validation failed: Invalid data type or null data');
      return false;
    }

    // For imported data, we need to be more flexible with validation
    // Check if it's an export file (has exportInfo) or regular canvas data
    const isExportFile = data.exportInfo || data.metadata?.exported;
    const isCanvasData = data.canvasProducts !== undefined || data.connections !== undefined;

    if (!isExportFile && !isCanvasData && !data.metadata) {
      console.warn('Data validation failed: Missing required structure (no exportInfo, canvas data, or metadata)');
      return false;
    }

    // Additional validation for canvas data
    if (data.canvasProducts && !Array.isArray(data.canvasProducts)) {
      console.warn('Data validation failed: canvasProducts must be an array');
      return false;
    }

    if (data.connections && !Array.isArray(data.connections)) {
      console.warn('Data validation failed: connections must be an array');
      return false;
    }

    console.log('Data validation passed');
    return true;
  }

  /**
   * Create backup of current data
   */
  createBackup(data) {
    if (!this.isSupported) return false;

    try {
      const timestamp = this.generateTimestamp();
      const backupKey = `plom_backup_${timestamp}`;
      
      const backupData = {
        ...data,
        backupInfo: {
          timestamp: new Date().toISOString(),
          originalKey: 'canvas_state',
          version: DEFAULT_DATA.metadata.version
        }
      };

      localStorage.setItem(backupKey, JSON.stringify(backupData));
      
      // Clean old backups
      this.cleanOldBackups();
      
      console.log(`Backup created: ${backupKey}`);
      return backupKey;
    } catch (error) {
      console.error('Error creating backup:', error);
      return false;
    }
  }

  /**
   * Clean old backups to maintain storage limit
   */
  cleanOldBackups() {
    try {
      const backupKeys = Object.keys(localStorage)
        .filter(key => key.startsWith('plom_backup_'))
        .sort()
        .reverse(); // Most recent first

      const maxBackups = this.loadData(STORAGE_KEYS.SETTINGS, DEFAULT_DATA.settings).maxBackups || 10;

      if (backupKeys.length > maxBackups) {
        const keysToRemove = backupKeys.slice(maxBackups);
        keysToRemove.forEach(key => {
          localStorage.removeItem(key);
          console.log(`Removed old backup: ${key}`);
        });
      }
    } catch (error) {
      console.error('Error cleaning old backups:', error);
    }
  }

  /**
   * Get list of available backups
   */
  getAvailableBackups() {
    if (!this.isSupported) return [];

    try {
      return Object.keys(localStorage)
        .filter(key => key.startsWith('plom_backup_'))
        .map(key => {
          try {
            const data = JSON.parse(localStorage.getItem(key));
            return {
              key,
              timestamp: data.backupInfo?.timestamp,
              version: data.backupInfo?.version,
              size: localStorage.getItem(key).length
            };
          } catch {
            return null;
          }
        })
        .filter(Boolean)
        .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
    } catch (error) {
      console.error('Error getting available backups:', error);
      return [];
    }
  }

  /**
   * Restore from backup
   */
  restoreFromBackup(backupKey) {
    try {
      const backupData = this.loadData(backupKey);
      if (!backupData) {
        throw new Error('Backup not found');
      }

      // Remove backup-specific metadata
      const { backupInfo, ...restoreData } = backupData;

      return restoreData;
    } catch (error) {
      console.error('Error restoring from backup:', error);
      return null;
    }
  }

  /**
   * Delete a specific backup
   */
  deleteBackup(backupKey) {
    if (!this.isSupported) {
      console.warn('Storage not supported, cannot delete backup');
      return false;
    }

    try {
      if (!backupKey.startsWith('plom_backup_')) {
        throw new Error('Invalid backup key');
      }

      const backupExists = localStorage.getItem(backupKey);
      if (!backupExists) {
        throw new Error('Backup not found');
      }

      localStorage.removeItem(backupKey);
      console.log(`Backup deleted successfully: ${backupKey}`);
      return true;
    } catch (error) {
      console.error('Error deleting backup:', error);
      return false;
    }
  }

  /**
   * Delete multiple backups
   */
  deleteMultipleBackups(backupKeys) {
    if (!this.isSupported) {
      console.warn('Storage not supported, cannot delete backups');
      return { success: false, deletedCount: 0, errors: ['Storage not supported'] };
    }

    const results = {
      success: true,
      deletedCount: 0,
      errors: []
    };

    backupKeys.forEach(backupKey => {
      try {
        if (this.deleteBackup(backupKey)) {
          results.deletedCount++;
        } else {
          results.errors.push(`Failed to delete ${backupKey}`);
        }
      } catch (error) {
        results.errors.push(`Error deleting ${backupKey}: ${error.message}`);
      }
    });

    if (results.errors.length > 0) {
      results.success = false;
    }

    return results;
  }

  /**
   * Export data as downloadable file
   */
  exportToFile(data, filename = null) {
    try {
      const timestamp = this.generateTimestamp();
      const exportFilename = filename || `plom-export-${timestamp}.json`;
      
      const exportData = {
        ...data,
        exportInfo: {
          timestamp: new Date().toISOString(),
          version: DEFAULT_DATA.metadata.version,
          application: 'PlomDesign'
        }
      };

      const blob = new Blob([JSON.stringify(exportData, null, 2)], {
        type: 'application/json'
      });

      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = exportFilename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      console.log(`Data exported to file: ${exportFilename}`);
      return true;
    } catch (error) {
      console.error('Error exporting to file:', error);
      return false;
    }
  }

  /**
   * Import data from file with support for large files
   */
  async importFromFile(file) {
    return new Promise((resolve, reject) => {
      if (!file) {
        reject(new Error('No file provided'));
        return;
      }

      // Validate file type
      if (!file.name.toLowerCase().endsWith('.json')) {
        reject(new Error('Invalid file type. Please select a JSON file.'));
        return;
      }

      // Optional size warning for very large files (500MB+) but don't block
      const warningSize = 500 * 1024 * 1024; // 500MB
      if (file.size > warningSize) {
        console.warn(`Large file detected: ${Math.round(file.size / (1024 * 1024))}MB. Import may take longer.`);
      }

      const reader = new FileReader();

      reader.onload = (e) => {
        try {
          console.log(`Parsing imported file (${Math.round(file.size / 1024)}KB)...`);

          // Use setTimeout to allow UI to update before heavy parsing
          setTimeout(() => {
            try {
              const importedData = JSON.parse(e.target.result);
              console.log('Imported data structure:', Object.keys(importedData));

              // Validate imported data
              if (this.validateDataStructure(importedData)) {
                // Clean up the data for import
                let cleanData = { ...importedData };

                // Remove export-specific metadata but preserve other metadata
                if (cleanData.exportInfo) {
                  delete cleanData.exportInfo;
                }

                // Ensure we have the required structure
                if (!cleanData.canvasProducts) {
                  cleanData.canvasProducts = [];
                }
                if (!cleanData.connections) {
                  cleanData.connections = [];
                }
                if (!cleanData.selectedProducts) {
                  cleanData.selectedProducts = [];
                }

                // Add import metadata
                cleanData.metadata = {
                  ...cleanData.metadata,
                  imported: new Date().toISOString(),
                  version: DEFAULT_DATA.metadata.version,
                  importedFileSize: file.size
                };

                console.log('Import successful, cleaned data:', Object.keys(cleanData));
                resolve(cleanData);
              } else {
                reject(new Error('Invalid file format. The file does not contain valid PlomDesign data.'));
              }
            } catch (parseError) {
              console.error('Error parsing imported file:', parseError);
              if (parseError instanceof SyntaxError) {
                reject(new Error('Invalid JSON format. Please check that the file is a valid JSON file.'));
              } else {
                reject(new Error('Error parsing file: ' + parseError.message));
              }
            }
          }, 10); // Small delay to allow UI updates

        } catch (error) {
          console.error('Error processing imported file:', error);
          reject(new Error('Error processing file: ' + error.message));
        }
      };

      reader.onerror = () => {
        reject(new Error('Error reading file. Please try again.'));
      };

      // Add progress tracking for large files
      reader.onprogress = (e) => {
        if (e.lengthComputable && file.size > warningSize) {
          const percentComplete = (e.loaded / e.total) * 100;
          console.log(`Reading file: ${Math.round(percentComplete)}% complete`);
        }
      };

      reader.readAsText(file);
    });
  }

  /**
   * Clear all stored data
   */
  clearAllData() {
    if (!this.isSupported) return false;

    try {
      Object.values(STORAGE_KEYS).forEach(key => {
        localStorage.removeItem(key);
      });

      // Remove backups
      Object.keys(localStorage)
        .filter(key => key.startsWith('plom_backup_'))
        .forEach(key => localStorage.removeItem(key));

      console.log('All data cleared successfully');
      return true;
    } catch (error) {
      console.error('Error clearing data:', error);
      return false;
    }
  }

  /**
   * Get storage usage information
   */
  getStorageInfo() {
    if (!this.isSupported) {
      return { supported: false };
    }

    try {
      let totalSize = 0;
      let itemCount = 0;
      const items = {};

      for (let key in localStorage) {
        if (key.startsWith('plom_')) {
          const size = localStorage.getItem(key).length;
          totalSize += size;
          itemCount++;
          items[key] = size;
        }
      }

      return {
        supported: true,
        totalSize,
        itemCount,
        items,
        formattedSize: this.formatBytes(totalSize)
      };
    } catch (error) {
      console.error('Error getting storage info:', error);
      return { supported: true, error: error.message };
    }
  }

  /**
   * Format bytes to human readable format
   */
  formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}

// Create singleton instance
export const dataStorage = new DataStorageManager();

export default dataStorage;
