import React, { createContext, useContext, useState, useEffect } from 'react';
import { productCategories, defaultProducts } from '../data/products';

// Create context
const SettingsContext = createContext();

// Export the hook for using context
export const useSettings = () => useContext(SettingsContext);

export const SettingsProvider = ({ children }) => {
  // STATE
  const [settings, setSettings] = useState(() => {
    // Default settings
    const defaultSettings = {
      visibleCategories: productCategories.reduce((acc, cat) => ({ ...acc, [cat.name]: true }), {}),
      removedCategories: [],
      deletedCategories: [],
      removedSubcategories: [],
      deletedSubcategories: [],
      removedProducts: [],
      deletedProducts: [],
      showGrid: true,
      autoSave: false,
      autoSaveInterval: 30
    };

    try {
      const saved = localStorage.getItem('plumber-app-settings');
      return saved ? { ...defaultSettings, ...JSON.parse(saved) } : defaultSettings;
    } catch (error) {
      return defaultSettings;
    }
  });

  const [customProducts, setCustomProducts] = useState(() => {
    try {
      const saved = localStorage.getItem('plumber-app-custom-products');
      return saved ? JSON.parse(saved) : [];
    } catch (error) {
      return [];
    }
  });

  const [customCategories, setCustomCategories] = useState(() => {
    try {
      const saved = localStorage.getItem('plumber-app-custom-categories');
      return saved ? JSON.parse(saved) : [];
    } catch (error) {
      return [];
    }
  });

  const [savedProjects, setSavedProjects] = useState(() => {
    try {
      const saved = localStorage.getItem('plumber-app-projects');
      return saved ? JSON.parse(saved) : [];
    } catch (error) {
      return [];
    }
  });

  // Safe localStorage operations with quota handling
  const safeSetLocalStorage = (key, data) => {
    try {
      const jsonString = JSON.stringify(data);
      // Check if the data is too large (> 4MB as a safety margin)
      if (jsonString.length > 4 * 1024 * 1024) {
        console.warn(`Data for ${key} is too large, skipping localStorage save`);
        return false;
      }
      localStorage.setItem(key, jsonString);
      return true;
    } catch (error) {
      if (error.name === 'QuotaExceededError') {
        console.warn('localStorage quota exceeded, clearing old data...');
        // Clear some old data to make space
        try {
          localStorage.removeItem('plumber-app-projects');
          localStorage.removeItem('plumber-app-custom-categories');
          // Try again with essential data only
          localStorage.setItem(key, JSON.stringify(data));
          return true;
        } catch (retryError) {
          console.error('Failed to save to localStorage even after cleanup:', retryError);
          return false;
        }
      } else {
        console.error('Error saving to localStorage:', error);
        return false;
      }
    }
  };

  // Save data to localStorage with error handling
  useEffect(() => {
    safeSetLocalStorage('plumber-app-settings', settings);
  }, [settings]);

  useEffect(() => {
    safeSetLocalStorage('plumber-app-custom-products', customProducts);
  }, [customProducts]);

  useEffect(() => {
    safeSetLocalStorage('plumber-app-custom-categories', customCategories);
  }, [customCategories]);

  useEffect(() => {
    safeSetLocalStorage('plumber-app-projects', savedProjects);
  }, [savedProjects]);

  // Check localStorage usage and clean up if needed
  const checkAndCleanupStorage = () => {
    try {
      // Estimate current localStorage usage
      let totalSize = 0;
      for (let key in localStorage) {
        if (localStorage.hasOwnProperty(key)) {
          totalSize += localStorage[key].length;
        }
      }

      // If usage is over 8MB, clean up non-essential data
      if (totalSize > 8 * 1024 * 1024) {
        console.warn('localStorage usage high, cleaning up...');
        // Remove oldest projects first
        const projects = savedProjects.slice(-5); // Keep only last 5 projects
        setSavedProjects(projects);

        // Clear any other non-essential data
        for (let key in localStorage) {
          if (key.startsWith('plumber-app-') &&
              !['plumber-app-settings', 'plumber-app-custom-products'].includes(key)) {
            localStorage.removeItem(key);
          }
        }
      }
    } catch (error) {
      console.error('Error checking localStorage:', error);
    }
  };

  // Basic Setting Functions
  const updateSetting = (key, value) => setSettings(prev => ({
    ...prev,
    [key]: value
  }));

  const updateCategoryVisibility = (categoryName, visible) =>
    setSettings(prev => ({
      ...prev,
      visibleCategories: { ...prev.visibleCategories, [categoryName]: visible }
    }));

  const resetCategoryVisibility = () => setSettings(prev => ({
    ...prev,
    visibleCategories: productCategories.reduce((acc, cat) => ({ ...acc, [cat.name]: true }), {}),
    removedCategories: [],
    deletedCategories: [],
    removedSubcategories: [],
    deletedSubcategories: [],
    removedProducts: [],
    deletedProducts: []
  }));

  // Category Functions
  const removeCategory = (categoryName) => {
    setSettings(prev => ({
      ...prev,
      removedCategories: [...prev.removedCategories, categoryName],
      visibleCategories: { ...prev.visibleCategories, [categoryName]: false }
    }));
  };

  const restoreCategory = (categoryName) => {
    setSettings(prev => ({
      ...prev,
      removedCategories: prev.removedCategories.filter(cat => cat !== categoryName),
      visibleCategories: { ...prev.visibleCategories, [categoryName]: true }
    }));
  };

  const permanentlyDeleteCategory = (categoryName, productHandlingOption = 'delete') => {
    try {
      // Find all products in this category (both default and custom)
      const affectedProducts = customProducts.filter(product =>
        product.category === categoryName
      );

      // Always delete all products in the category when permanently deleting the category
      // This ensures complete removal regardless of the productHandlingOption
      affectedProducts.forEach(product => {
        permanentlyDeleteProduct(product.id);
      });

      // Remove the category from custom categories if it exists
      setCustomCategories(prev =>
        prev.filter(cat => cat.name !== categoryName)
      );

      // Update settings to track deletion and clean up all related data
      setSettings(prev => ({
        ...prev,
        // Remove from removed categories list
        removedCategories: prev.removedCategories.filter(cat => cat !== categoryName),
        // Add to deleted categories list
        deletedCategories: [...prev.deletedCategories, categoryName],
        // Remove from visible categories
        visibleCategories: Object.fromEntries(
          Object.entries(prev.visibleCategories).filter(([key]) => key !== categoryName)
        ),
        // Remove all subcategories of this category from removed list
        removedSubcategories: prev.removedSubcategories.filter(
          item => item.category !== categoryName
        ),
        // Add all subcategories of this category to deleted list
        deletedSubcategories: [
          ...prev.deletedSubcategories,
          ...prev.removedSubcategories
            .filter(item => item.category === categoryName)
            .map(item => ({ category: categoryName, subcategory: item.subcategory }))
        ]
      }));

      console.log(`Category "${categoryName}" permanently deleted with ${affectedProducts.length} affected products`);
      return { success: true, affectedProducts: affectedProducts.length };
    } catch (error) {
      console.error('Error permanently deleting category:', error);
      return { success: false, error: error.message };
    }
  };

  const removeSubcategory = (categoryName, subcategoryName) => {
    setSettings(prev => ({
      ...prev,
      removedSubcategories: [...prev.removedSubcategories, { category: categoryName, subcategory: subcategoryName }]
    }));
  };

  const restoreSubcategory = (categoryName, subcategoryName) => {
    setSettings(prev => ({
      ...prev,
      removedSubcategories: prev.removedSubcategories.filter(
        item => !(item.category === categoryName && item.subcategory === subcategoryName)
      )
    }));
  };

  const permanentlyDeleteSubcategory = (categoryName, subcategoryName, productHandlingOption = 'orphan') => {
    try {
      // Find all products in this subcategory
      const affectedProducts = customProducts.filter(product =>
        product.category === categoryName && product.subcategory === subcategoryName
      );

      // Handle products based on the selected option
      switch (productHandlingOption) {
        case 'delete':
          // Delete all products in this subcategory
          affectedProducts.forEach(product => {
            permanentlyDeleteProduct(product.id);
          });
          break;

        case 'reassign':
          // This would require a target subcategory - handled in the UI
          break;

        case 'orphan':
        default:
          // Mark products as orphaned by setting subcategory to 'Other'
          setCustomProducts(prev =>
            prev.map(product =>
              product.category === categoryName && product.subcategory === subcategoryName
                ? { ...product, subcategory: 'Other', orphaned: true }
                : product
            )
          );
          break;
      }

      // Remove the subcategory from custom categories if it exists
      setCustomCategories(prev =>
        prev.map(cat =>
          cat.name === categoryName
            ? { ...cat, subcategories: cat.subcategories.filter(sub => sub !== subcategoryName) }
            : cat
        )
      );

      // Update settings to track deletion
      setSettings(prev => ({
        ...prev,
        removedSubcategories: prev.removedSubcategories.filter(
          item => !(item.category === categoryName && item.subcategory === subcategoryName)
        ),
        deletedSubcategories: [...prev.deletedSubcategories, { category: categoryName, subcategory: subcategoryName }]
      }));

      console.log(`Subcategory "${subcategoryName}" in "${categoryName}" permanently deleted with ${affectedProducts.length} affected products`);
      return { success: true, affectedProducts: affectedProducts.length };
    } catch (error) {
      console.error('Error permanently deleting subcategory:', error);
      return { success: false, error: error.message };
    }
  };

  const removeProduct = (productId) => {
    setSettings(prev => ({
      ...prev,
      removedProducts: [...prev.removedProducts, productId]
    }));
  };

  const restoreProduct = (productId) => {
    setSettings(prev => ({
      ...prev,
      removedProducts: prev.removedProducts.filter(id => id !== productId)
    }));
  };

  const permanentlyDeleteProduct = (productId) => {
    // Remove from custom products array
    setCustomProducts(prev => prev.filter(p => p.id !== productId));

    // Update settings to track deletion and remove from removed products
    setSettings(prev => ({
      ...prev,
      removedProducts: prev.removedProducts.filter(id => id !== productId),
      deletedProducts: [...prev.deletedProducts, productId]
    }));
  };

  const addCustomCategory = (categoryName) => {
    try {
      if (!categoryName) return null;
      const trimmedName = categoryName.trim();

      if (!trimmedName) return null;

      // Check if category already exists (avoid calling getAllCategories to prevent circular dependency)
      const existingDefaultCategory = productCategories.find(cat =>
        cat.name.toLowerCase() === trimmedName.toLowerCase()
      );
      const existingCustomCategory = customCategories.find(cat =>
        cat.name.toLowerCase() === trimmedName.toLowerCase()
      );

      if (existingDefaultCategory || existingCustomCategory) {
        const existingCategory = existingDefaultCategory || existingCustomCategory;
        console.warn(`Category "${trimmedName}" already exists`);
        return existingCategory;
      }

      const newCategory = {
        name: trimmedName,
        subcategories: [],
        isCustom: true,
        createdAt: new Date().toISOString()
      };

      setCustomCategories(prev => [...prev, newCategory]);

      // Also add to visible categories
      setSettings(prev => ({
        ...prev,
        visibleCategories: {
          ...prev.visibleCategories,
          [trimmedName]: true
        }
      }));

      console.log(`Category "${trimmedName}" created successfully`);
      return newCategory;
    } catch (error) {
      console.error('Error adding custom category:', error);
      return null;
    }
  };

  const addCustomSubcategory = (categoryName, subcategoryName) => {
    try {
      if (!categoryName || !subcategoryName) return;

      const trimmedCategoryName = categoryName.trim();
      const trimmedSubcategoryName = subcategoryName.trim();

      if (!trimmedCategoryName || !trimmedSubcategoryName) return;

      // Check if subcategory already exists in this category
      const allCategories = getAllCategories();
      const parentCategory = allCategories.find(cat => cat.name === trimmedCategoryName);

      if (parentCategory && parentCategory.subcategories.includes(trimmedSubcategoryName)) {
        console.warn(`Subcategory "${trimmedSubcategoryName}" already exists in "${trimmedCategoryName}"`);
        return;
      }

      // Check if it's a custom category
      const existingIndex = customCategories.findIndex(cat => cat.name === trimmedCategoryName);

      if (existingIndex !== -1) {
        // Add to existing custom category
        setCustomCategories(prev =>
          prev.map((cat, index) =>
            index === existingIndex
              ? { ...cat, subcategories: [...cat.subcategories, trimmedSubcategoryName] }
              : cat
          )
        );
      } else {
        // Create a new custom category with the subcategory
        const newCategory = {
          name: trimmedCategoryName,
          subcategories: [trimmedSubcategoryName],
          isCustom: true,
          createdAt: new Date().toISOString(),
          isExtension: true // This indicates it extends a default category
        };

        setCustomCategories(prev => [...prev, newCategory]);

        // Also add to visible categories if not already there
        setSettings(prev => ({
          ...prev,
          visibleCategories: {
            ...prev.visibleCategories,
            [trimmedCategoryName]: true
          }
        }));
      }

      console.log(`Subcategory "${trimmedSubcategoryName}" added to "${trimmedCategoryName}"`);
    } catch (error) {
      console.error('Error adding custom subcategory:', error);
    }
  };

  const ensureCategoryExists = (categoryName, subcategoryName) => {
    try {
      if (!categoryName || !subcategoryName) return;

      const trimmedCategoryName = categoryName.trim();
      const trimmedSubcategoryName = subcategoryName.trim();

      if (!trimmedCategoryName || !trimmedSubcategoryName) return;

      const allCategories = getAllCategories();
      const existingCategory = allCategories.find(cat => cat.name === trimmedCategoryName);

      if (!existingCategory) {
        // Category doesn't exist, create it
        const newCategory = addCustomCategory(trimmedCategoryName);
        if (newCategory) {
          // Add the subcategory to the new category
          addCustomSubcategory(trimmedCategoryName, trimmedSubcategoryName);
        }
      } else {
        // Category exists, check if subcategory exists
        if (!existingCategory.subcategories.includes(trimmedSubcategoryName)) {
          // Subcategory doesn't exist, add it
          addCustomSubcategory(trimmedCategoryName, trimmedSubcategoryName);
        }
      }

      // Ensure the category is visible
      setSettings(prev => ({
        ...prev,
        visibleCategories: {
          ...prev.visibleCategories,
          [trimmedCategoryName]: true
        }
      }));
    } catch (error) {
      console.error('Error ensuring category exists:', error);
    }
  };

  // Get all categories (default + custom) excluding deleted ones
  const getAllCategories = () => {
    // Create a deep copy of default categories to avoid mutations
    // Convert subcategory objects to strings for consistency
    // Filter out deleted categories
    const allCategories = productCategories
      .filter(cat => !settings.deletedCategories.includes(cat.name))
      .map(cat => ({
        ...cat,
        subcategories: cat.subcategories
          .map(sub => typeof sub === 'object' ? sub.name : sub)
          .filter(sub => !settings.deletedSubcategories.some(
            deleted => deleted.category === cat.name && deleted.subcategory === sub
          ))
      }));

    // Add custom categories (excluding deleted ones)
    customCategories
      .filter(customCat => !settings.deletedCategories.includes(customCat.name))
      .forEach(customCat => {
        if (customCat.isExtension) {
          // Extend existing category
          const existingIndex = allCategories.findIndex(cat => cat.name === customCat.name);
          if (existingIndex !== -1) {
            // Create a new object to avoid mutation
            const filteredSubcategories = customCat.subcategories.filter(sub =>
              !allCategories[existingIndex].subcategories.includes(sub) &&
              !settings.deletedSubcategories.some(
                deleted => deleted.category === customCat.name && deleted.subcategory === sub
              )
            );
            allCategories[existingIndex] = {
              ...allCategories[existingIndex],
              subcategories: [
                ...allCategories[existingIndex].subcategories,
                ...filteredSubcategories
              ]
            };
          } else {
            // Category doesn't exist in defaults, add as new
            const filteredSubcategories = customCat.subcategories.filter(sub =>
              !settings.deletedSubcategories.some(
                deleted => deleted.category === customCat.name && deleted.subcategory === sub
              )
            );
            allCategories.push({
              ...customCat,
              subcategories: filteredSubcategories
            });
          }
        } else {
          // New custom category
          const filteredSubcategories = customCat.subcategories.filter(sub =>
            !settings.deletedSubcategories.some(
              deleted => deleted.category === customCat.name && deleted.subcategory === sub
            )
          );
          allCategories.push({
            ...customCat,
            subcategories: filteredSubcategories
          });
        }
      });

    return allCategories;
  };

  // Helper function to get product count for a category
  const getCategoryProductCount = (categoryName) => {
    return customProducts.filter(product => product.category === categoryName).length;
  };

  // Helper function to get product count for a subcategory
  const getSubcategoryProductCount = (categoryName, subcategoryName) => {
    return customProducts.filter(product =>
      product.category === categoryName && product.subcategory === subcategoryName
    ).length;
  };

  // Helper function to get all available categories for reassignment
  const getAvailableCategories = (excludeCategory = null) => {
    return getAllCategories()
      .filter(cat => cat.name !== excludeCategory)
      .map(cat => cat.name);
  };

  // Helper function to get all available subcategories for reassignment
  const getAvailableSubcategories = (categoryName, excludeSubcategory = null) => {
    const category = getAllCategories().find(cat => cat.name === categoryName);
    if (!category) return [];

    return category.subcategories.filter(sub => sub !== excludeSubcategory);
  };

  // Function to reassign products to a new category/subcategory
  const reassignProducts = (fromCategory, fromSubcategory, toCategory, toSubcategory) => {
    try {
      setCustomProducts(prev =>
        prev.map(product => {
          if (fromSubcategory) {
            // Reassigning from specific subcategory
            if (product.category === fromCategory && product.subcategory === fromSubcategory) {
              return { ...product, category: toCategory, subcategory: toSubcategory, orphaned: false };
            }
          } else {
            // Reassigning entire category
            if (product.category === fromCategory) {
              return { ...product, category: toCategory, subcategory: toSubcategory, orphaned: false };
            }
          }
          return product;
        })
      );

      return { success: true };
    } catch (error) {
      console.error('Error reassigning products:', error);
      return { success: false, error: error.message };
    }
  };

  const addCustomProduct = (productData) => {
    // Check and cleanup storage before adding new product
    checkAndCleanupStorage();

    // Ensure category and subcategory exist in the category management system
    ensureCategoryExists(productData.category, productData.subcategory);

    // Default placeholder image
    const defaultImage = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA2MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjYwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjZjNmNGY2Ii8+CjxwYXRoIGQ9Ik0yNSAyMGwtNS01djEwaDEwdi0xMGwtNSA1eiIgZmlsbD0iIzlmYTZiNyIvPgo8L3N2Zz4=';

    // Process and validate image data
    let finalImage = defaultImage;

    if (productData.image) {
      if (typeof productData.image === 'string') {
        if (productData.image.startsWith('data:image/')) {
          finalImage = productData.image;
        } else if (productData.image.startsWith('http')) {
          finalImage = productData.image;
        } else {
          finalImage = productData.image;
        }
      }
    }

    const newProduct = {
      ...productData,
      id: `custom-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      isCustom: true,
      createdAt: new Date().toISOString(),
      // Ensure consistent fields
      size: productData.diameter || productData.size || 'N/A',
      image: finalImage,
      // Ensure price is a number
      price: typeof productData.price === 'number' ? productData.price : parseFloat(productData.price) || 0
    };

    setCustomProducts(prev => [...prev, newProduct]);
    return newProduct;
  };

  const removeCustomProduct = (productId) => {
    setCustomProducts(prev => prev.filter(p => p.id !== productId));
  };

  const updateCustomProduct = (productId, updates) => {
    setCustomProducts(prev =>
      prev.map(p => p.id === productId ? { ...p, ...updates } : p)
    );
  };

  // Add/update products from template to existing catalog
  const replaceProductCatalogWithTemplate = (templateProducts) => {
    try {
      // Check and cleanup storage before updating
      checkAndCleanupStorage();

      let addedCount = 0;
      let updatedCount = 0;
      const currentProducts = [...customProducts];

      // Process each template product
      templateProducts.forEach(templateProduct => {
        // Ensure category and subcategory exist
        ensureCategoryExists(templateProduct.category, templateProduct.subcategory);

        // Default placeholder image
        const defaultImage = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA2MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjYwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjZjNmNGY2Ii8+CjxwYXRoIGQ9Ik0yNSAyMGwtNS01djEwaDEwdi0xMGwtNSA1eiIgZmlsbD0iIzlmYTZiNyIvPgo8L3N2Zz4=';

        // Process and validate image data
        let finalImage = defaultImage;
        if (templateProduct.image) {
          if (typeof templateProduct.image === 'string') {
            if (templateProduct.image.startsWith('data:image/') ||
                templateProduct.image.startsWith('http')) {
              finalImage = templateProduct.image;
            }
          }
        }

        // Check if product already exists in current catalog
        const existingProductIndex = currentProducts.findIndex(product =>
          product.name.toLowerCase() === templateProduct.name.toLowerCase() &&
          product.category.toLowerCase() === templateProduct.category.toLowerCase() &&
          product.subcategory.toLowerCase() === templateProduct.subcategory.toLowerCase()
        );

        // Prepare the product data
        const productData = {
          ...templateProduct,
          isCustom: true,
          updatedAt: new Date().toISOString(),
          // Ensure consistent fields
          size: templateProduct.diameter || templateProduct.size || 'N/A',
          image: finalImage,
          // Ensure price is a number
          price: typeof templateProduct.price === 'number' ? templateProduct.price : parseFloat(templateProduct.price) || 0,
          // Remove template-specific fields
          mainCatalogId: undefined,
          syncedAt: undefined,
          orphaned: undefined
        };

        if (existingProductIndex !== -1) {
          // Update existing product
          currentProducts[existingProductIndex] = {
            ...currentProducts[existingProductIndex],
            ...productData,
            id: currentProducts[existingProductIndex].id, // Keep original ID
            createdAt: currentProducts[existingProductIndex].createdAt // Keep original creation date
          };
          updatedCount++;
        } else {
          // Add new product
          const newProduct = {
            ...productData,
            id: `custom-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
            createdAt: new Date().toISOString()
          };
          currentProducts.push(newProduct);
          addedCount++;
        }
      });

      // Update the custom products catalog with merged data
      setCustomProducts(currentProducts);

      console.log(`Product catalog updated: ${addedCount} added, ${updatedCount} updated`);
      return {
        success: true,
        addedCount,
        updatedCount,
        totalProcessed: templateProducts.length
      };

    } catch (error) {
      console.error('Error updating product catalog with template:', error);
      return { success: false, error: error.message };
    }
  };

  // Project management functions
  const saveProject = (projectData, name) => {
    const project = {
      id: Date.now().toString(),
      name: name || `Project ${new Date().toLocaleDateString()}`,
      data: projectData,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    setSavedProjects(prev => {
      const existing = prev.find(p => p.name === project.name);
      if (existing) {
        return prev.map(p =>
          p.name === project.name
            ? { ...project, id: existing.id, createdAt: existing.createdAt }
            : p
        );
      }
      return [...prev, project];
    });

    return project;
  };

  const loadProject = (projectId) => {
    return savedProjects.find(p => p.id === projectId);
  };

  const deleteProject = (projectId) => {
    setSavedProjects(prev => prev.filter(p => p.id !== projectId));
  };

  const clearAllData = () => {
    try {
      // Clear projects
      setSavedProjects([]);
      localStorage.removeItem('plumber-app-projects');

      // Clear custom products
      setCustomProducts([]);
      localStorage.removeItem('plumber-app-custom-products');

      // Clear custom categories
      setCustomCategories([]);
      localStorage.removeItem('plumber-app-custom-categories');

      // Hide ALL categories (including default ones) and mark all products as deleted
      const allCategoryNames = productCategories.map(cat => cat.name);
      const allProductIds = defaultProducts.map(product => product.id);

      // Create settings that effectively hide everything
      const clearAllSettings = {
        visibleCategories: productCategories.reduce((acc, cat) => ({ ...acc, [cat.name]: false }), {}),
        removedCategories: [...allCategoryNames], // Mark all default categories as removed
        deletedCategories: [...allCategoryNames], // Mark all default categories as deleted
        removedSubcategories: [],
        deletedSubcategories: [],
        removedProducts: [...allProductIds], // Mark all default products as removed
        deletedProducts: [...allProductIds], // Mark all default products as deleted
        showGrid: true,
        autoSave: false,
        autoSaveInterval: 30
      };
      setSettings(clearAllSettings);

      console.log('All application data has been successfully cleared, including default products and categories');
      return { success: true, message: 'All data cleared successfully' };
    } catch (error) {
      console.error('Error clearing all data:', error);
      return { success: false, message: 'Failed to clear all data', error: error.message };
    }
  };

  // Enhanced function to get data summary for confirmation dialogs
  const getDataSummary = () => {
    return {
      projectsCount: savedProjects.length,
      customProductsCount: customProducts.length,
      customCategoriesCount: customCategories.length,
      defaultProductsCount: defaultProducts.length,
      defaultCategoriesCount: productCategories.length,
      hasModifiedSettings: Object.keys(settings.removedCategories || []).length > 0 ||
                          Object.keys(settings.deletedCategories || []).length > 0 ||
                          Object.keys(settings.removedProducts || []).length > 0 ||
                          Object.keys(settings.deletedProducts || []).length > 0
    };
  };

  // Function to restore default data (for undo functionality)
  const restoreDefaultData = () => {
    try {
      // Reset settings to show all default categories
      const defaultSettings = {
        visibleCategories: productCategories.reduce((acc, cat) => ({ ...acc, [cat.name]: true }), {}),
        removedCategories: [],
        deletedCategories: [],
        removedSubcategories: [],
        deletedSubcategories: [],
        removedProducts: [],
        deletedProducts: [],
        showGrid: true,
        autoSave: false,
        autoSaveInterval: 30
      };
      setSettings(defaultSettings);

      console.log('Default data has been restored');
      return { success: true, message: 'Default data restored successfully' };
    } catch (error) {
      console.error('Error restoring default data:', error);
      return { success: false, message: 'Failed to restore default data', error: error.message };
    }
  };

  // Clear all custom categories and subcategories
  const clearCategoriesData = () => {
    // Clear custom categories
    setCustomCategories([]);
    localStorage.removeItem('plumber-app-custom-categories');

    // Reset category visibility to show all default categories
    const resetVisibility = productCategories.reduce((acc, cat) => ({ ...acc, [cat.name]: true }), {});

    // Reset category-related settings
    setSettings(prev => ({
      ...prev,
      visibleCategories: resetVisibility,
      removedCategories: [],
      deletedCategories: [],
      removedSubcategories: [],
      deletedSubcategories: []
    }));

    console.log('All custom categories and subcategories have been cleared.');
  };

  // Export/Import functions
  const exportSettings = () => {
    const exportData = {
      version: '2.0',
      exportedAt: new Date().toISOString(),
      settings,
      customProducts,
      customCategories,
      savedProjects
    };

    return JSON.stringify(exportData, null, 2);
  };

  const validateImportData = (data) => {
    const errors = [];
    const warnings = [];

    // Check version compatibility
    if (!data.version) {
      warnings.push('Import file version not specified - assuming legacy format');
    } else if (data.version !== '2.0' && data.version !== '1.0') {
      errors.push(`Unsupported import file version: ${data.version}`);
    }

    // Validate data structure
    if (data.version === '2.0') {
      if (!data.data) {
        errors.push('Invalid file structure - missing data section');
        return { isValid: false, errors, warnings };
      }

      // Validate categories
      if (data.data.categories) {
        if (data.data.categories.custom && !Array.isArray(data.data.categories.custom)) {
          errors.push('Invalid custom categories format');
        }
      }

      // Validate products
      if (data.data.products) {
        if (data.data.products.custom && !Array.isArray(data.data.products.custom)) {
          errors.push('Invalid custom products format');
        }
      }

      // Validate projects
      if (data.data.savedProjects && !Array.isArray(data.data.savedProjects)) {
        errors.push('Invalid saved projects format');
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  };

  const analyzeImportData = (data) => {
    const analysis = {
      categories: { new: 0, existing: 0, conflicts: [] },
      subcategories: { new: 0, existing: 0, conflicts: [] },
      products: { new: 0, existing: 0, conflicts: [] },
      projects: { new: 0, existing: 0, conflicts: [] },
      settings: { changes: [] }
    };

    // Handle both legacy and new format
    const importData = data.version === '2.0' ? data.data : data;

    // Analyze categories
    if (importData.categories?.custom || importData.customCategories) {
      const importCategories = importData.categories?.custom || importData.customCategories || [];
      importCategories.forEach(category => {
        const existing = customCategories.find(c => c.name === category.name);
        if (existing) {
          analysis.categories.existing++;
          if (JSON.stringify(existing) !== JSON.stringify(category)) {
            analysis.categories.conflicts.push({
              name: category.name,
              type: 'modified',
              existing,
              imported: category
            });
          }
        } else {
          analysis.categories.new++;
        }
      });
    }

    // Analyze products
    if (importData.products?.custom || importData.customProducts) {
      const importProducts = importData.products?.custom || importData.customProducts || [];
      importProducts.forEach(product => {
        const existing = customProducts.find(p => p.id === product.id);
        if (existing) {
          analysis.products.existing++;
          if (JSON.stringify(existing) !== JSON.stringify(product)) {
            analysis.products.conflicts.push({
              id: product.id,
              name: product.name,
              type: 'modified',
              existing,
              imported: product
            });
          }
        } else {
          analysis.products.new++;
        }
      });
    }

    // Analyze projects
    if (importData.savedProjects) {
      importData.savedProjects.forEach(project => {
        const existing = savedProjects.find(p => p.name === project.name);
        if (existing) {
          analysis.projects.existing++;
          if (JSON.stringify(existing.data) !== JSON.stringify(project.data)) {
            analysis.projects.conflicts.push({
              name: project.name,
              type: 'modified',
              existing,
              imported: project
            });
          }
        } else {
          analysis.projects.new++;
        }
      });
    }

    // Analyze settings
    if (importData.settings) {
      Object.keys(importData.settings).forEach(key => {
        if (settings[key] !== undefined &&
            JSON.stringify(settings[key]) !== JSON.stringify(importData.settings[key])) {
          analysis.settings.changes.push({
            key,
            current: settings[key],
            imported: importData.settings[key]
          });
        }
      });
    }

    return analysis;
  };

  const importSettings = (jsonData, options = {}) => {
    try {
      const data = typeof jsonData === 'string' ? JSON.parse(jsonData) : jsonData;

      if (data.settings) setSettings(data.settings);
      if (data.customProducts) setCustomProducts(data.customProducts);
      if (data.customCategories) setCustomCategories(data.customCategories);
      if (data.savedProjects) setSavedProjects(data.savedProjects);

      return { success: true };
    } catch (error) {
      console.error('Error importing settings:', error);
      return {
        success: false,
        errors: ['Failed to parse import file: ' + error.message]
      };
    }
  };

  const resetSettings = () => {
    const defaultSettings = {
      visibleCategories: productCategories.reduce((acc, cat) => ({ ...acc, [cat.name]: true }), {}),
      removedCategories: [],
      deletedCategories: [],
      removedSubcategories: [],
      deletedSubcategories: [],
      removedProducts: [],
      deletedProducts: [],
      showGrid: true,
      autoSave: false,
      autoSaveInterval: 30
    };
    setSettings(defaultSettings);
  };

  // Emergency function to clear all localStorage data
  const clearAllLocalStorage = () => {
    try {
      // Clear all plumber app data from localStorage
      const keysToRemove = [];
      for (let key in localStorage) {
        if (key.startsWith('plumber-app-')) {
          keysToRemove.push(key);
        }
      }
      keysToRemove.forEach(key => localStorage.removeItem(key));

      // Reset all state to defaults
      setCustomProducts([]);
      setCustomCategories([]);
      setSavedProjects([]);
      setSettings({
        visibleCategories: productCategories.reduce((acc, cat) => ({ ...acc, [cat.name]: true }), {}),
        removedCategories: [],
        deletedCategories: [],
        removedSubcategories: [],
        deletedSubcategories: [],
        removedProducts: [],
        deletedProducts: [],
        showGrid: true,
        autoSave: false,
        autoSaveInterval: 30
      });

      console.log('All localStorage data cleared successfully');
      return true;
    } catch (error) {
      console.error('Error clearing localStorage:', error);
      return false;
    }
  };

  // CONTEXT VALUE - provide all needed functions and state
  const value = {
    settings,
    customProducts,
    customCategories,
    savedProjects,
    updateSetting,
    updateCategoryVisibility,
    resetCategoryVisibility,
    removeCategory,
    restoreCategory,
    permanentlyDeleteCategory,
    removeSubcategory,
    restoreSubcategory,
    permanentlyDeleteSubcategory,
    removeProduct,
    restoreProduct,
    permanentlyDeleteProduct,
    addCustomCategory,
    addCustomSubcategory,
    ensureCategoryExists,
    getAllCategories,
    getCategoryProductCount,
    getSubcategoryProductCount,
    getAvailableCategories,
    getAvailableSubcategories,
    reassignProducts,
    addCustomProduct,
    removeCustomProduct,
    updateCustomProduct,
    replaceProductCatalogWithTemplate,
    saveProject,
    loadProject,
    deleteProject,
    clearAllData,
    clearCategoriesData,
    resetSettings,
    getDataSummary,
    restoreDefaultData,
    exportSettings,
    importSettings,
    validateImportData,
    analyzeImportData,
    clearAllLocalStorage,
    checkAndCleanupStorage
  };

  return (
    <SettingsContext.Provider value={value}>
      {children}
    </SettingsContext.Provider>
  );
};
