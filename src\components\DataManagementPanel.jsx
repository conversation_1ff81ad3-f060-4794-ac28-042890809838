import React, { useState } from 'react';
import { 
  Download, 
  Upload, 
  Save, 
  Database, 
  Trash2, 
  Setting<PERSON>, 
  Clock, 
  HardDrive,
  RefreshCw,
  AlertTriangle
} from 'lucide-react';
import { useData } from '../contexts/DataContext';

const DataManagementPanel = ({ isOpen, onClose }) => {
  const {
    storageInfo,
    lastSaveTime,
    autoSaveEnabled,
    hasLoadedData,
    saveApplicationData,
    exportData,
    importData,
    getBackups,
    restoreFromBackup,
    clearAllData,
    saveSettings
  } = useData();

  const [activeTab, setActiveTab] = useState('overview');
  const [backups, setBackups] = useState([]);
  const [isLoading, setIsLoading] = useState(false);

  React.useEffect(() => {
    if (isOpen) {
      setBackups(getBackups());
    }
  }, [isOpen, getBackups]);

  const handleExport = async () => {
    setIsLoading(true);
    try {
      // Get current app data (this would come from your main app state)
      const currentData = {
        canvasProducts: [], // This should be passed from parent
        connections: [],
        selectedProducts: [],
        metadata: {
          version: '1.0.0',
          exported: new Date().toISOString()
        }
      };

      const result = exportData(currentData);
      if (result.success) {
        window.showDataNotification?.({
          type: 'success',
          message: 'Data exported successfully',
          action: 'export'
        });
      } else {
        throw new Error(result.message);
      }
    } catch (error) {
      window.showDataNotification?.({
        type: 'error',
        message: 'Export failed',
        details: error.message,
        action: 'export'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleImport = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    setIsLoading(true);
    try {
      const result = await importData(file);
      if (result.success) {
        window.showDataNotification?.({
          type: 'success',
          message: 'Data imported successfully',
          details: 'Your workspace has been updated',
          action: 'import'
        });
        // Here you would update your main app state with result.data
      } else {
        throw new Error(result.message);
      }
    } catch (error) {
      window.showDataNotification?.({
        type: 'error',
        message: 'Import failed',
        details: error.message,
        action: 'import'
      });
    } finally {
      setIsLoading(false);
      event.target.value = ''; // Reset file input
    }
  };

  const handleRestoreBackup = async (backupKey) => {
    setIsLoading(true);
    try {
      const result = restoreFromBackup(backupKey);
      if (result.success) {
        window.showDataNotification?.({
          type: 'success',
          message: 'Backup restored successfully',
          action: 'backup'
        });
        // Here you would update your main app state with result.data
      } else {
        throw new Error(result.message);
      }
    } catch (error) {
      window.showDataNotification?.({
        type: 'error',
        message: 'Restore failed',
        details: error.message,
        action: 'backup'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleClearData = async () => {
    if (!window.confirm('Are you sure you want to clear all data? This action cannot be undone.')) {
      return;
    }

    setIsLoading(true);
    try {
      const result = clearAllData();
      if (result.success) {
        window.showDataNotification?.({
          type: 'success',
          message: 'All data cleared successfully',
          action: 'save'
        });
        onClose();
      } else {
        throw new Error(result.message);
      }
    } catch (error) {
      window.showDataNotification?.({
        type: 'error',
        message: 'Clear data failed',
        details: error.message
      });
    } finally {
      setIsLoading(false);
    }
  };

  const toggleAutoSave = async () => {
    const newSettings = {
      autoSave: !autoSaveEnabled,
      autoLoad: true,
      backupInterval: 300000,
      maxBackups: 10
    };

    const result = saveSettings(newSettings);
    if (result.success) {
      window.showDataNotification?.({
        type: 'success',
        message: `Auto-save ${newSettings.autoSave ? 'enabled' : 'disabled'}`,
        action: 'save'
      });
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'Never';
    return new Date(dateString).toLocaleString();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-dark-900/80 backdrop-blur-sm z-[9999] flex items-center justify-center">
      <div className="bg-dark-800 border border-dark-600 rounded-xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-dark-600">
          <div className="flex items-center space-x-3">
            <Database className="w-6 h-6 text-primary-500" />
            <h2 className="text-xl font-semibold text-white">Data Management</h2>
          </div>
          <button
            onClick={onClose}
            className="text-dark-400 hover:text-white transition-colors"
          >
            ×
          </button>
        </div>

        {/* Tabs */}
        <div className="flex border-b border-dark-600">
          {[
            { id: 'overview', label: 'Overview', icon: Database },
            { id: 'backups', label: 'Backups', icon: Clock },
            { id: 'settings', label: 'Settings', icon: Settings }
          ].map(tab => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center space-x-2 px-6 py-3 text-sm font-medium transition-colors ${
                activeTab === tab.id
                  ? 'text-primary-400 border-b-2 border-primary-500'
                  : 'text-dark-400 hover:text-white'
              }`}
            >
              <tab.icon className="w-4 h-4" />
              <span>{tab.label}</span>
            </button>
          ))}
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[60vh]">
          {/* Overview Tab */}
          {activeTab === 'overview' && (
            <div className="space-y-6">
              {/* Storage Info */}
              <div className="bg-dark-700/50 rounded-lg p-4">
                <div className="flex items-center space-x-2 mb-3">
                  <HardDrive className="w-5 h-5 text-primary-500" />
                  <h3 className="font-medium text-white">Storage Information</h3>
                </div>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-dark-400">Total Size:</span>
                    <span className="text-white ml-2">{storageInfo?.formattedSize || '0 Bytes'}</span>
                  </div>
                  <div>
                    <span className="text-dark-400">Items:</span>
                    <span className="text-white ml-2">{storageInfo?.itemCount || 0}</span>
                  </div>
                  <div>
                    <span className="text-dark-400">Last Save:</span>
                    <span className="text-white ml-2">{formatDate(lastSaveTime)}</span>
                  </div>
                  <div>
                    <span className="text-dark-400">Auto-save:</span>
                    <span className={`ml-2 ${autoSaveEnabled ? 'text-green-400' : 'text-red-400'}`}>
                      {autoSaveEnabled ? 'Enabled' : 'Disabled'}
                    </span>
                  </div>
                </div>
              </div>

              {/* Quick Actions */}
              <div className="grid grid-cols-2 gap-4">
                <button
                  onClick={handleExport}
                  disabled={isLoading}
                  className="flex items-center justify-center space-x-2 p-4 bg-primary-600 hover:bg-primary-700 disabled:opacity-50 rounded-lg transition-colors"
                >
                  <Download className="w-5 h-5" />
                  <span>Export Data</span>
                </button>

                <label className="flex items-center justify-center space-x-2 p-4 bg-green-600 hover:bg-green-700 rounded-lg transition-colors cursor-pointer">
                  <Upload className="w-5 h-5" />
                  <span>Import Data</span>
                  <input
                    type="file"
                    accept=".json"
                    onChange={handleImport}
                    className="hidden"
                    disabled={isLoading}
                  />
                </label>
              </div>

              {/* Danger Zone */}
              <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-4">
                <div className="flex items-center space-x-2 mb-3">
                  <AlertTriangle className="w-5 h-5 text-red-500" />
                  <h3 className="font-medium text-red-400">Danger Zone</h3>
                </div>
                <p className="text-sm text-dark-300 mb-3">
                  Clear all stored data including canvas layouts, connections, and settings.
                </p>
                <button
                  onClick={handleClearData}
                  disabled={isLoading}
                  className="flex items-center space-x-2 px-4 py-2 bg-red-600 hover:bg-red-700 disabled:opacity-50 rounded text-sm transition-colors"
                >
                  <Trash2 className="w-4 h-4" />
                  <span>Clear All Data</span>
                </button>
              </div>
            </div>
          )}

          {/* Backups Tab */}
          {activeTab === 'backups' && (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="font-medium text-white">Available Backups</h3>
                <button
                  onClick={() => setBackups(getBackups())}
                  className="flex items-center space-x-2 px-3 py-1 bg-dark-700 hover:bg-dark-600 rounded text-sm transition-colors"
                >
                  <RefreshCw className="w-4 h-4" />
                  <span>Refresh</span>
                </button>
              </div>

              {backups.length === 0 ? (
                <div className="text-center py-8 text-dark-400">
                  <Database className="w-12 h-12 mx-auto mb-3 opacity-50" />
                  <p>No backups available</p>
                </div>
              ) : (
                <div className="space-y-2">
                  {backups.map((backup) => (
                    <div
                      key={backup.key}
                      className="flex items-center justify-between p-3 bg-dark-700/50 rounded-lg"
                    >
                      <div>
                        <div className="text-white text-sm font-medium">
                          {formatDate(backup.timestamp)}
                        </div>
                        <div className="text-dark-400 text-xs">
                          Size: {Math.round(backup.size / 1024)} KB
                        </div>
                      </div>
                      <button
                        onClick={() => handleRestoreBackup(backup.key)}
                        disabled={isLoading}
                        className="px-3 py-1 bg-primary-600 hover:bg-primary-700 disabled:opacity-50 rounded text-sm transition-colors"
                      >
                        Restore
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {/* Settings Tab */}
          {activeTab === 'settings' && (
            <div className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 bg-dark-700/50 rounded-lg">
                  <div>
                    <h4 className="font-medium text-white">Auto-save</h4>
                    <p className="text-sm text-dark-400">Automatically save your work every 5 minutes</p>
                  </div>
                  <button
                    onClick={toggleAutoSave}
                    className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                      autoSaveEnabled ? 'bg-primary-600' : 'bg-dark-600'
                    }`}
                  >
                    <span
                      className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                        autoSaveEnabled ? 'translate-x-6' : 'translate-x-1'
                      }`}
                    />
                  </button>
                </div>

                <div className="flex items-center justify-between p-4 bg-dark-700/50 rounded-lg">
                  <div>
                    <h4 className="font-medium text-white">Auto-load</h4>
                    <p className="text-sm text-dark-400">Automatically restore your work when the app starts</p>
                  </div>
                  <button
                    className="relative inline-flex h-6 w-11 items-center rounded-full bg-primary-600"
                  >
                    <span className="inline-block h-4 w-4 transform rounded-full bg-white translate-x-6" />
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default DataManagementPanel;
