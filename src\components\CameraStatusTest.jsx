import React, { useState, useRef, useEffect } from 'react';
import { Camera, X, CheckCircle, AlertCircle } from 'lucide-react';

/**
 * Test component to verify camera status indicator functionality
 */
const CameraStatusTest = () => {
  const [showCameraStream, setShowCameraStream] = useState(false);
  const [isCameraReady, setIsCameraReady] = useState(false);
  const [showCameraReadyIndicator, setShowCameraReadyIndicator] = useState(false);
  const [isProcessingImage, setIsProcessingImage] = useState(false);
  const [error, setError] = useState('');
  const [debugLog, setDebugLog] = useState([]);
  
  const videoRef = useRef(null);
  const canvasRef = useRef(null);
  const streamRef = useRef(null);

  const addDebugLog = (message) => {
    const timestamp = new Date().toLocaleTimeString();
    setDebugLog(prev => [...prev.slice(-9), `${timestamp}: ${message}`]);
  };

  const startCameraStream = async () => {
    try {
      setError('');
      setIsProcessingImage(true);
      setIsCameraReady(false);
      setShowCameraReadyIndicator(false);
      addDebugLog('Starting camera initialization...');

      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        throw new Error('Camera not supported on this device');
      }

      let stream = null;
      const constraints = [
        {
          video: {
            width: { ideal: 1280 },
            height: { ideal: 720 },
            facingMode: { exact: 'environment' }
          }
        },
        {
          video: {
            width: { ideal: 1280 },
            height: { ideal: 720 },
            facingMode: 'user'
          }
        },
        { video: true }
      ];

      for (const constraint of constraints) {
        try {
          stream = await navigator.mediaDevices.getUserMedia(constraint);
          addDebugLog(`Camera stream obtained with constraint: ${JSON.stringify(constraint.video)}`);
          break;
        } catch (err) {
          addDebugLog(`Failed constraint: ${JSON.stringify(constraint.video)}`);
          continue;
        }
      }

      if (!stream) {
        throw new Error('Unable to access any camera');
      }

      streamRef.current = stream;
      addDebugLog(`Video ref status: ${videoRef.current ? 'Available' : 'NULL'}`);

      // Set camera stream to active first to render the video element
      setShowCameraStream(true);

      // Wait a moment for the video element to be rendered
      await new Promise(resolve => setTimeout(resolve, 100));

      addDebugLog(`Video ref status after delay: ${videoRef.current ? 'Available' : 'NULL'}`);

      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        addDebugLog('Video srcObject set, starting enhanced readiness detection...');

        // Enhanced approach: Start video playback immediately and monitor readiness
        const video = videoRef.current;

        // Start playing the video immediately
        try {
          await video.play();
          addDebugLog('Video play() called successfully');
        } catch (playError) {
          addDebugLog(`Video play() failed: ${playError.message}, continuing with readiness checks...`);
        }

        // Enhanced readiness detection with multiple strategies
        await new Promise((resolve, reject) => {
          let resolved = false;
          let checkCount = 0;
          const maxChecks = 100; // 10 seconds total

          const checkVideoReadiness = () => {
            if (resolved) return;
            checkCount++;

            const readyState = video.readyState;
            const width = video.videoWidth;
            const height = video.videoHeight;
            const paused = video.paused;
            const ended = video.ended;

            addDebugLog(`Check #${checkCount}: readyState=${readyState}, width=${width}, height=${height}, paused=${paused}, ended=${ended}`);

            // Multiple readiness criteria - any of these should work
            const isReady = (
              // Primary criteria: full readiness
              (readyState >= 2 && width > 0 && height > 0 && !paused && !ended) ||
              // Secondary criteria: has dimensions and some data
              (readyState >= 1 && width > 0 && height > 0) ||
              // Fallback criteria: just has dimensions (for some browsers)
              (width > 0 && height > 0 && checkCount > 10)
            );

            if (isReady) {
              resolved = true;
              addDebugLog(`Camera is ready! Criteria met: readyState=${readyState}, dimensions=${width}x${height}`);
              setIsCameraReady(true);
              setShowCameraReadyIndicator(true);

              setTimeout(() => {
                addDebugLog('Hiding ready indicator after 3 seconds');
                setShowCameraReadyIndicator(false);
              }, 3000);

              resolve();
            } else if (checkCount >= maxChecks) {
              // Final timeout - force resolution if video has any dimensions
              if (width > 0 && height > 0) {
                addDebugLog(`Forcing camera ready after ${maxChecks} checks - video has dimensions ${width}x${height}`);
                resolved = true;
                setIsCameraReady(true);
                setShowCameraReadyIndicator(true);
                setTimeout(() => setShowCameraReadyIndicator(false), 3000);
                resolve();
              } else {
                addDebugLog(`Camera readiness failed after ${maxChecks} checks - no video dimensions`);
                reject(new Error('Camera readiness timeout - no video dimensions detected'));
              }
            } else {
              // Continue checking
              setTimeout(checkVideoReadiness, 100);
            }
          };

          // Start checking immediately
          checkVideoReadiness();

          // Additional event listeners as backup
          const onLoadedData = () => {
            if (!resolved) {
              addDebugLog('loadeddata event fired, triggering readiness check');
              checkVideoReadiness();
            }
          };

          const onCanPlay = () => {
            if (!resolved) {
              addDebugLog('canplay event fired, triggering readiness check');
              checkVideoReadiness();
            }
          };

          const onLoadedMetadata = () => {
            if (!resolved) {
              addDebugLog('loadedmetadata event fired, triggering readiness check');
              checkVideoReadiness();
            }
          };

          video.addEventListener('loadeddata', onLoadedData);
          video.addEventListener('canplay', onCanPlay);
          video.addEventListener('loadedmetadata', onLoadedMetadata);

          // Cleanup function
          const cleanup = () => {
            video.removeEventListener('loadeddata', onLoadedData);
            video.removeEventListener('canplay', onCanPlay);
            video.removeEventListener('loadedmetadata', onLoadedMetadata);
          };

          // Final timeout
          setTimeout(() => {
            if (!resolved) {
              cleanup();
              addDebugLog('Final timeout reached - forcing resolution if possible');
              if (video.videoWidth > 0 && video.videoHeight > 0) {
                resolved = true;
                setIsCameraReady(true);
                setShowCameraReadyIndicator(true);
                setTimeout(() => setShowCameraReadyIndicator(false), 3000);
                resolve();
              } else {
                reject(new Error('Video loading timeout - no dimensions after 15 seconds'));
              }
            } else {
              cleanup();
            }
          }, 15000);
        });
      } else {
        addDebugLog('ERROR: Video element not available even after delay!');
      }

      setIsProcessingImage(false);
      addDebugLog('Camera stream setup complete');
    } catch (error) {
      addDebugLog(`Camera error: ${error.message}`);
      setIsProcessingImage(false);
      setIsCameraReady(false);
      setShowCameraStream(false);
      setShowCameraReadyIndicator(false);
      setError(error.message);
    }
  };

  const stopCameraStream = () => {
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
      streamRef.current = null;
    }
    setShowCameraStream(false);
    setIsCameraReady(false);
    setShowCameraReadyIndicator(false);
    addDebugLog('Camera stream stopped');
  };

  const capturePhoto = () => {
    if (!videoRef.current || !canvasRef.current || !isCameraReady) {
      addDebugLog('Capture failed: Camera not ready');
      return;
    }

    try {
      const video = videoRef.current;
      const canvas = canvasRef.current;
      const context = canvas.getContext('2d');

      canvas.width = video.videoWidth || 640;
      canvas.height = video.videoHeight || 480;
      context.drawImage(video, 0, 0, canvas.width, canvas.height);

      const base64Data = canvas.toDataURL('image/jpeg', 0.85);
      addDebugLog(`Photo captured: ${base64Data.length} bytes`);
      stopCameraStream();
    } catch (error) {
      addDebugLog(`Capture error: ${error.message}`);
    }
  };

  useEffect(() => {
    return () => {
      stopCameraStream();
    };
  }, []);

  return (
    <div className="p-6 bg-white rounded-lg shadow-lg max-w-4xl mx-auto">
      <h2 className="text-2xl font-bold mb-4 flex items-center">
        <Camera className="mr-2 text-blue-600" />
        Camera Status Indicator Test
      </h2>
      
      <div className="grid md:grid-cols-2 gap-6">
        {/* Camera Interface */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Camera Interface</h3>
          
          {error && (
            <div className="p-3 rounded-lg bg-red-50 border border-red-200 text-red-800">
              <p className="text-sm font-medium">{error}</p>
            </div>
          )}

          {!showCameraStream ? (
            <button
              onClick={startCameraStream}
              disabled={isProcessingImage}
              className={`w-full flex items-center justify-center px-4 py-3 rounded-lg font-medium transition-colors ${
                isProcessingImage
                  ? 'bg-gray-400 text-white cursor-not-allowed'
                  : 'bg-blue-600 text-white hover:bg-blue-700'
              }`}
            >
              <Camera size={16} className="mr-2" />
              {isProcessingImage ? 'Starting camera...' : 'Start Camera'}
            </button>
          ) : (
            <div className="space-y-3">
              <div className="relative bg-black rounded-lg overflow-hidden">
                <video
                  ref={videoRef}
                  autoPlay
                  playsInline
                  className="w-full h-48 object-cover"
                />
                
                {/* Status Indicators */}
                {!isCameraReady && (
                  <div className="absolute top-3 left-1/2 transform -translate-x-1/2">
                    <div className="bg-black bg-opacity-70 text-white px-3 py-1 rounded-lg text-sm flex items-center">
                      <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-2"></div>
                      Initializing camera...
                    </div>
                  </div>
                )}
                
                {isCameraReady && showCameraReadyIndicator && (
                  <div className="absolute top-3 left-1/2 transform -translate-x-1/2">
                    <div className="bg-green-600 bg-opacity-90 text-white px-3 py-1 rounded-lg text-sm flex items-center">
                      <div className="w-2 h-2 bg-green-300 rounded-full mr-2"></div>
                      Camera ready
                    </div>
                  </div>
                )}
                
                <div className="absolute bottom-3 left-1/2 transform -translate-x-1/2 flex space-x-3">
                  <button
                    onClick={capturePhoto}
                    disabled={!isCameraReady}
                    className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                      isCameraReady
                        ? 'bg-white text-gray-900 hover:bg-gray-100'
                        : 'bg-gray-400 text-gray-600 cursor-not-allowed'
                    }`}
                  >
                    <Camera size={14} className="inline mr-1" />
                    {isCameraReady ? 'Capture' : 'Preparing...'}
                  </button>
                  <button
                    onClick={stopCameraStream}
                    className="px-3 py-2 bg-red-600 text-white rounded-lg text-sm font-medium hover:bg-red-700 transition-colors"
                  >
                    <X size={14} className="inline mr-1" />
                    Cancel
                  </button>
                </div>
              </div>
              <canvas ref={canvasRef} className="hidden" />
            </div>
          )}
        </div>

        {/* Debug Information */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Debug Information</h3>
          
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <span className="font-medium">Camera Stream:</span>
              <span className={`px-2 py-1 rounded text-xs ${showCameraStream ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}`}>
                {showCameraStream ? 'Active' : 'Inactive'}
              </span>
            </div>
            
            <div className="flex items-center space-x-2">
              <span className="font-medium">Camera Ready:</span>
              <span className={`px-2 py-1 rounded text-xs ${isCameraReady ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}`}>
                {isCameraReady ? 'Ready' : 'Not Ready'}
              </span>
            </div>
            
            <div className="flex items-center space-x-2">
              <span className="font-medium">Ready Indicator:</span>
              <span className={`px-2 py-1 rounded text-xs ${showCameraReadyIndicator ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'}`}>
                {showCameraReadyIndicator ? 'Showing' : 'Hidden'}
              </span>
            </div>
            
            <div className="flex items-center space-x-2">
              <span className="font-medium">Processing:</span>
              <span className={`px-2 py-1 rounded text-xs ${isProcessingImage ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800'}`}>
                {isProcessingImage ? 'Processing' : 'Idle'}
              </span>
            </div>
          </div>

          <div className="bg-gray-50 rounded-lg p-3">
            <h4 className="font-medium mb-2">Debug Log:</h4>
            <div className="space-y-1 max-h-48 overflow-y-auto">
              {debugLog.map((log, index) => (
                <div key={index} className="text-xs text-gray-600 font-mono">
                  {log}
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CameraStatusTest;
