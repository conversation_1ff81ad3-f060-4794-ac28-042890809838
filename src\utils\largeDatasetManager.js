/**
 * Large Dataset Manager
 * Handles enterprise-scale datasets with efficient indexing and memory management
 */

class LargeDatasetManager {
  constructor(options = {}) {
    this.maxMemoryUsage = options.maxMemoryUsage || 0.8; // 80% of available memory
    this.chunkSize = options.chunkSize || 10000;
    this.indexingEnabled = options.indexingEnabled !== false;
    this.compressionEnabled = options.compressionEnabled || false;
    
    // Data storage
    this.data = {
      products: new Map(),
      connections: new Map(),
      categories: new Map(),
      metadata: {}
    };
    
    // Indexes for fast searching
    this.indexes = {
      productsByCategory: new Map(),
      productsByName: new Map(),
      productsByPrice: new Map(),
      connectionsByProduct: new Map(),
      spatialIndex: new Map() // For canvas positioning
    };
    
    // Memory management
    this.memoryStats = {
      totalItems: 0,
      indexedItems: 0,
      memoryUsage: 0,
      lastCleanup: Date.now()
    };
    
    // Performance tracking
    this.performanceMetrics = {
      searchTimes: [],
      indexingTimes: [],
      memoryCleanups: 0
    };
    
    this.isProcessing = false;
    this.worker = null;
    
    this.initializeWorker();
  }

  /**
   * Initialize Web Worker for background processing
   */
  initializeWorker() {
    try {
      // Create worker from blob to avoid external file dependency
      const workerCode = `
        class DataWorker {
          constructor() {
            this.indexes = new Map();
          }
          
          createIndex(data, indexType) {
            const index = new Map();
            
            switch (indexType) {
              case 'category':
                data.forEach(item => {
                  const key = item.category || 'uncategorized';
                  if (!index.has(key)) index.set(key, []);
                  index.get(key).push(item.id);
                });
                break;
                
              case 'name':
                data.forEach(item => {
                  const words = (item.name || '').toLowerCase().split(' ');
                  words.forEach(word => {
                    if (word.length > 2) {
                      if (!index.has(word)) index.set(word, []);
                      index.get(word).push(item.id);
                    }
                  });
                });
                break;
                
              case 'price':
                data.forEach(item => {
                  const priceRange = this.getPriceRange(item.price || 0);
                  if (!index.has(priceRange)) index.set(priceRange, []);
                  index.get(priceRange).push(item.id);
                });
                break;
            }
            
            return Array.from(index.entries());
          }
          
          getPriceRange(price) {
            if (price < 10) return '0-10';
            if (price < 50) return '10-50';
            if (price < 100) return '50-100';
            if (price < 500) return '100-500';
            return '500+';
          }
          
          searchIndex(query, indexData, limit = 100) {
            const results = new Set();
            const queryLower = query.toLowerCase();
            
            for (const [key, ids] of indexData) {
              if (key.includes(queryLower)) {
                ids.forEach(id => results.add(id));
                if (results.size >= limit) break;
              }
            }
            
            return Array.from(results).slice(0, limit);
          }
        }
        
        const worker = new DataWorker();
        
        self.onmessage = function(e) {
          const { type, data, options } = e.data;
          
          switch (type) {
            case 'CREATE_INDEX':
              const indexData = worker.createIndex(data.items, data.indexType);
              self.postMessage({
                type: 'INDEX_CREATED',
                indexType: data.indexType,
                indexData,
                id: data.id
              });
              break;
              
            case 'SEARCH':
              const results = worker.searchIndex(data.query, data.indexData, options?.limit);
              self.postMessage({
                type: 'SEARCH_RESULTS',
                results,
                query: data.query,
                id: data.id
              });
              break;
          }
        };
      `;
      
      const blob = new Blob([workerCode], { type: 'application/javascript' });
      this.worker = new Worker(URL.createObjectURL(blob));
      
      this.worker.onmessage = (e) => {
        this.handleWorkerMessage(e.data);
      };
      
    } catch (error) {
      console.warn('Web Worker not available, falling back to main thread processing');
    }
  }

  /**
   * Handle messages from worker
   */
  handleWorkerMessage(message) {
    switch (message.type) {
      case 'INDEX_CREATED':
        this.indexes[message.indexType] = new Map(message.indexData);
        this.memoryStats.indexedItems += message.indexData.length;
        break;
        
      case 'SEARCH_RESULTS':
        // Handle search results
        if (this.pendingSearches && this.pendingSearches.has(message.id)) {
          const resolve = this.pendingSearches.get(message.id);
          resolve(message.results);
          this.pendingSearches.delete(message.id);
        }
        break;
    }
  }

  /**
   * Load large dataset efficiently
   */
  async loadDataset(dataset) {
    this.isProcessing = true;
    const startTime = performance.now();
    
    try {
      // Clear existing data
      this.clearData();
      
      // Process products in chunks
      if (dataset.products && Array.isArray(dataset.products)) {
        await this.loadProductsInChunks(dataset.products);
      }
      
      // Process connections
      if (dataset.connections && Array.isArray(dataset.connections)) {
        await this.loadConnectionsInChunks(dataset.connections);
      }
      
      // Create indexes
      if (this.indexingEnabled) {
        await this.createIndexes();
      }
      
      // Update metadata
      this.data.metadata = {
        ...dataset.metadata,
        loadedAt: new Date().toISOString(),
        productCount: this.data.products.size,
        connectionCount: this.data.connections.size,
        indexCount: Object.keys(this.indexes).length
      };
      
      const loadTime = performance.now() - startTime;
      this.performanceMetrics.indexingTimes.push(loadTime);
      
      console.log(`Dataset loaded in ${loadTime.toFixed(2)}ms`);
      
    } catch (error) {
      console.error('Error loading dataset:', error);
      throw error;
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * Load products in chunks to prevent memory spikes
   */
  async loadProductsInChunks(products) {
    const totalProducts = products.length;
    
    for (let i = 0; i < products.length; i += this.chunkSize) {
      const chunk = products.slice(i, i + this.chunkSize);
      
      chunk.forEach(product => {
        this.data.products.set(product.id, product);
        
        // Add to category index
        const category = product.category || 'uncategorized';
        if (!this.data.categories.has(category)) {
          this.data.categories.set(category, []);
        }
        this.data.categories.get(category).push(product.id);
      });
      
      // Check memory usage and cleanup if needed
      if (i % (this.chunkSize * 5) === 0) {
        await this.checkMemoryUsage();
        // Yield control to prevent blocking
        await new Promise(resolve => setTimeout(resolve, 10));
      }
    }
    
    this.memoryStats.totalItems = this.data.products.size;
  }

  /**
   * Load connections in chunks
   */
  async loadConnectionsInChunks(connections) {
    for (let i = 0; i < connections.length; i += this.chunkSize) {
      const chunk = connections.slice(i, i + this.chunkSize);
      
      chunk.forEach(connection => {
        this.data.connections.set(connection.id, connection);
        
        // Add to product connection index
        const fromId = connection.from;
        const toId = connection.to;
        
        if (!this.indexes.connectionsByProduct.has(fromId)) {
          this.indexes.connectionsByProduct.set(fromId, []);
        }
        if (!this.indexes.connectionsByProduct.has(toId)) {
          this.indexes.connectionsByProduct.set(toId, []);
        }
        
        this.indexes.connectionsByProduct.get(fromId).push(connection.id);
        this.indexes.connectionsByProduct.get(toId).push(connection.id);
      });
      
      // Yield control
      if (i % this.chunkSize === 0) {
        await new Promise(resolve => setTimeout(resolve, 5));
      }
    }
  }

  /**
   * Create search indexes
   */
  async createIndexes() {
    if (!this.worker) {
      return this.createIndexesMainThread();
    }
    
    const products = Array.from(this.data.products.values());
    
    // Create indexes using worker
    const indexTypes = ['category', 'name', 'price'];
    
    for (const indexType of indexTypes) {
      await new Promise((resolve) => {
        const id = Math.random().toString(36);
        
        if (!this.pendingSearches) {
          this.pendingSearches = new Map();
        }
        this.pendingSearches.set(id, resolve);
        
        this.worker.postMessage({
          type: 'CREATE_INDEX',
          data: { items: products, indexType, id }
        });
      });
    }
  }

  /**
   * Create indexes on main thread (fallback)
   */
  createIndexesMainThread() {
    const products = Array.from(this.data.products.values());
    
    // Category index
    products.forEach(product => {
      const category = product.category || 'uncategorized';
      if (!this.indexes.productsByCategory.has(category)) {
        this.indexes.productsByCategory.set(category, []);
      }
      this.indexes.productsByCategory.get(category).push(product.id);
    });
    
    // Name index (for searching)
    products.forEach(product => {
      const words = (product.name || '').toLowerCase().split(' ');
      words.forEach(word => {
        if (word.length > 2) {
          if (!this.indexes.productsByName.has(word)) {
            this.indexes.productsByName.set(word, []);
          }
          this.indexes.productsByName.get(word).push(product.id);
        }
      });
    });
  }

  /**
   * Fast search using indexes
   */
  async search(query, options = {}) {
    const startTime = performance.now();
    const limit = options.limit || 100;
    const results = new Set();
    
    if (!query || query.length < 2) {
      return [];
    }
    
    const queryLower = query.toLowerCase();
    
    // Search in name index
    for (const [word, productIds] of this.indexes.productsByName) {
      if (word.includes(queryLower)) {
        productIds.forEach(id => results.add(id));
        if (results.size >= limit) break;
      }
    }
    
    // Search in category index if needed
    if (results.size < limit) {
      for (const [category, productIds] of this.indexes.productsByCategory) {
        if (category.toLowerCase().includes(queryLower)) {
          productIds.slice(0, limit - results.size).forEach(id => results.add(id));
          if (results.size >= limit) break;
        }
      }
    }
    
    const searchTime = performance.now() - startTime;
    this.performanceMetrics.searchTimes.push(searchTime);
    
    // Convert IDs to products
    const products = Array.from(results)
      .slice(0, limit)
      .map(id => this.data.products.get(id))
      .filter(Boolean);
    
    return products;
  }

  /**
   * Get products by category efficiently
   */
  getProductsByCategory(category, limit = 1000) {
    const productIds = this.indexes.productsByCategory.get(category) || [];
    return productIds
      .slice(0, limit)
      .map(id => this.data.products.get(id))
      .filter(Boolean);
  }

  /**
   * Check memory usage and cleanup if needed
   */
  async checkMemoryUsage() {
    if (!performance.memory) return;
    
    const memory = performance.memory;
    const usageRatio = memory.usedJSHeapSize / memory.jsHeapSizeLimit;
    
    this.memoryStats.memoryUsage = usageRatio;
    
    if (usageRatio > this.maxMemoryUsage) {
      await this.performMemoryCleanup();
    }
  }

  /**
   * Perform memory cleanup
   */
  async performMemoryCleanup() {
    console.log('Performing memory cleanup...');
    
    // Clear old performance metrics
    this.performanceMetrics.searchTimes = this.performanceMetrics.searchTimes.slice(-100);
    this.performanceMetrics.indexingTimes = this.performanceMetrics.indexingTimes.slice(-10);
    
    // Force garbage collection if available
    if (window.gc && typeof window.gc === 'function') {
      window.gc();
    }
    
    this.memoryStats.lastCleanup = Date.now();
    this.performanceMetrics.memoryCleanups++;
    
    console.log('Memory cleanup completed');
  }

  /**
   * Clear all data
   */
  clearData() {
    this.data.products.clear();
    this.data.connections.clear();
    this.data.categories.clear();
    
    Object.keys(this.indexes).forEach(key => {
      this.indexes[key].clear();
    });
    
    this.memoryStats = {
      totalItems: 0,
      indexedItems: 0,
      memoryUsage: 0,
      lastCleanup: Date.now()
    };
  }

  /**
   * Get performance metrics
   */
  getPerformanceMetrics() {
    const avgSearchTime = this.performanceMetrics.searchTimes.length > 0 ?
      this.performanceMetrics.searchTimes.reduce((a, b) => a + b, 0) / this.performanceMetrics.searchTimes.length : 0;
    
    return {
      ...this.memoryStats,
      averageSearchTime: avgSearchTime,
      totalSearches: this.performanceMetrics.searchTimes.length,
      memoryCleanups: this.performanceMetrics.memoryCleanups
    };
  }

  /**
   * Destroy manager and cleanup resources
   */
  destroy() {
    this.clearData();
    
    if (this.worker) {
      this.worker.terminate();
      this.worker = null;
    }
    
    if (this.pendingSearches) {
      this.pendingSearches.clear();
    }
  }
}

export default LargeDatasetManager;
