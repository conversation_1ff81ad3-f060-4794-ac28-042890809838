import React, { useState, useEffect } from 'react';
import {
  X,
  AlertCircle,
  CheckCircle,
  AlertTriangle,
  Package,
  Tag,
  FolderOpen,
  Settings,
  ChevronDown,
  ChevronRight,
  Eye,
  EyeOff
} from 'lucide-react';

const ImportPreviewModal = ({
  isOpen,
  onClose,
  importData,
  analysis,
  onConfirmImport,
  onCancel
}) => {
  const [selectedTypes, setSelectedTypes] = useState({
    settings: true,
    categories: true,
    products: true,
    projects: true
  });

  const [importOptions, setImportOptions] = useState({
    replaceCategories: false,
    replaceProducts: false,
    replaceProjects: false
  });

  const [expandedSections, setExpandedSections] = useState({
    categories: false,
    products: false,
    projects: false,
    settings: false
  });

  const [showDetails, setShowDetails] = useState({});

  if (!isOpen || !importData || !analysis) return null;

  const toggleSection = (section) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const toggleDetails = (type, id) => {
    const key = `${type}-${id}`;
    setShowDetails(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  const handleTypeToggle = (type) => {
    setSelectedTypes(prev => ({
      ...prev,
      [type]: !prev[type]
    }));
  };

  const handleOptionToggle = (option) => {
    setImportOptions(prev => ({
      ...prev,
      [option]: !prev[option]
    }));
  };

  const handleConfirm = () => {
    const finalOptions = {
      importSettings: selectedTypes.settings,
      importCategories: selectedTypes.categories,
      importProducts: selectedTypes.products,
      importProjects: selectedTypes.projects,
      ...importOptions
    };
    onConfirmImport(finalOptions);
  };

  const getTotalChanges = () => {
    return (
      analysis.categories.new + analysis.categories.conflicts.length +
      analysis.products.new + analysis.products.conflicts.length +
      analysis.projects.new + analysis.projects.conflicts.length +
      analysis.settings.changes.length
    );
  };

  const renderSummaryCard = (title, icon, data, type) => {
    const hasConflicts = data.conflicts && data.conflicts.length > 0;
    const hasChanges = data.new > 0 || hasConflicts || (data.changes && data.changes.length > 0);

    return (
      <div className="border rounded-lg p-4 bg-white">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-2">
            {icon}
            <h4 className="font-medium text-gray-900">{title}</h4>
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={selectedTypes[type]}
                onChange={() => handleTypeToggle(type)}
                className="mr-2"
              />
              <span className="text-sm text-gray-600">Import</span>
            </label>
          </div>
          <button
            onClick={() => toggleSection(type)}
            className="text-gray-500 hover:text-gray-700"
          >
            {expandedSections[type] ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
          </button>
        </div>

        <div className="grid grid-cols-3 gap-4 text-sm">
          <div className="text-center">
            <div className="text-lg font-semibold text-green-600">{data.new || 0}</div>
            <div className="text-gray-600">New</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-semibold text-blue-600">{data.existing || 0}</div>
            <div className="text-gray-600">Existing</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-semibold text-orange-600">
              {data.conflicts?.length || data.changes?.length || 0}
            </div>
            <div className="text-gray-600">Conflicts</div>
          </div>
        </div>

        {expandedSections[type] && hasChanges && (
          <div className="mt-4 space-y-2">
            {/* Import options for this type */}
            {type !== 'settings' && (
              <div className="flex items-center space-x-4 p-2 bg-gray-50 rounded">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={importOptions[`replace${type.charAt(0).toUpperCase() + type.slice(1)}`]}
                    onChange={() => handleOptionToggle(`replace${type.charAt(0).toUpperCase() + type.slice(1)}`)}
                    className="mr-2"
                  />
                  <span className="text-sm">Replace existing {type}</span>
                </label>
              </div>
            )}

            {/* Show conflicts/changes */}
            {(data.conflicts || data.changes || []).map((item, index) => (
              <div key={index} className="border-l-4 border-orange-400 pl-3 py-2 bg-orange-50">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <AlertTriangle size={16} className="text-orange-600" />
                    <span className="font-medium text-sm">
                      {item.name || item.key || item.id}
                    </span>
                    <span className="text-xs bg-orange-200 text-orange-800 px-2 py-1 rounded">
                      {item.type || 'modified'}
                    </span>
                  </div>
                  <button
                    onClick={() => toggleDetails(type, index)}
                    className="text-orange-600 hover:text-orange-800"
                  >
                    {showDetails[`${type}-${index}`] ? <EyeOff size={14} /> : <Eye size={14} />}
                  </button>
                </div>
                
                {showDetails[`${type}-${index}`] && (
                  <div className="mt-2 text-xs space-y-2">
                    <div>
                      <div className="font-medium text-gray-700">Current:</div>
                      <pre className="bg-gray-100 p-2 rounded text-xs overflow-x-auto">
                        {JSON.stringify(item.existing || item.current, null, 2)}
                      </pre>
                    </div>
                    <div>
                      <div className="font-medium text-gray-700">Imported:</div>
                      <pre className="bg-blue-100 p-2 rounded text-xs overflow-x-auto">
                        {JSON.stringify(item.imported, null, 2)}
                      </pre>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div className="flex items-center space-x-3">
            <AlertCircle className="text-blue-600" size={24} />
            <div>
              <h2 className="text-xl font-semibold text-gray-900">Import Preview</h2>
              <p className="text-sm text-gray-600">
                Review the changes that will be made to your data
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X size={24} />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
          {/* Summary */}
          <div className="mb-6 p-4 bg-blue-50 rounded-lg">
            <div className="flex items-center space-x-2 mb-2">
              <CheckCircle className="text-blue-600" size={20} />
              <h3 className="font-medium text-blue-900">Import Summary</h3>
            </div>
            <p className="text-blue-800 text-sm">
              This import will make <strong>{getTotalChanges()}</strong> changes to your data.
              Review each section below and select what you want to import.
            </p>
          </div>

          {/* Data Type Cards */}
          <div className="space-y-4">
            {renderSummaryCard(
              'Application Settings',
              <Settings size={20} className="text-gray-600" />,
              analysis.settings,
              'settings'
            )}

            {renderSummaryCard(
              'Categories',
              <Tag size={20} className="text-purple-600" />,
              analysis.categories,
              'categories'
            )}

            {renderSummaryCard(
              'Products',
              <Package size={20} className="text-green-600" />,
              analysis.products,
              'products'
            )}

            {renderSummaryCard(
              'Projects',
              <FolderOpen size={20} className="text-blue-600" />,
              analysis.projects,
              'projects'
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t bg-gray-50">
          <div className="text-sm text-gray-600">
            Selected: {Object.values(selectedTypes).filter(Boolean).length} of 4 data types
          </div>
          <div className="flex space-x-3">
            <button
              onClick={onCancel}
              className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              onClick={handleConfirm}
              disabled={!Object.values(selectedTypes).some(Boolean)}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Import Selected Data
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ImportPreviewModal;
