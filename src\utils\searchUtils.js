/**
 * Enhanced search utilities for case-insensitive product search
 */

/**
 * Performs case-insensitive search across multiple product fields with multi-word support
 * @param {Array} products - Array of products to search
 * @param {string} searchTerm - Search term to match (supports multiple words)
 * @returns {Array} Filtered products that match ALL search terms
 */
export const searchProducts = (products, searchTerm) => {
  if (!searchTerm || !searchTerm.trim()) {
    return products;
  }

  // Split search term into individual words and clean them
  const searchWords = searchTerm
    .toLowerCase()
    .trim()
    .split(/\s+/)
    .filter(word => word.length > 0);

  if (searchWords.length === 0) {
    return products;
  }

  return products.filter(product => {
    // Get all searchable text from the product
    const searchableText = getProductSearchableText(product).toLowerCase();

    // Check if ALL search words are present in the searchable text
    return searchWords.every(word => searchableText.includes(word));
  });
};

/**
 * Extracts all searchable text from a product into a single string
 * @param {Object} product - Product object
 * @returns {string} Combined searchable text
 */
const getProductSearchableText = (product) => {
  const searchableFields = [
    product.name,
    product.category,
    product.subcategory,
    product.material,
    product.description,
    product.diameter,
    product.type,
    product.brand,
    product.model
  ];

  return searchableFields
    .filter(field => field && typeof field === 'string')
    .join(' ')
    .toLowerCase();
};

/**
 * Checks if a text contains the search term (case-insensitive, multi-word support)
 * @param {string} text - Text to search in
 * @param {string} searchTerm - Search term to find (supports multiple words)
 * @returns {boolean} True if text contains ALL search words
 */
export const containsSearchTerm = (text, searchTerm) => {
  if (!text || !searchTerm) return false;

  const searchWords = searchTerm
    .toLowerCase()
    .trim()
    .split(/\s+/)
    .filter(word => word.length > 0);

  if (searchWords.length === 0) return false;

  const lowerText = text.toLowerCase();
  return searchWords.every(word => lowerText.includes(word));
};

/**
 * Gets all search matches in a text string (supports multi-word search)
 * @param {string} text - Text to search in
 * @param {string} searchTerm - Search term to find (supports multiple words)
 * @returns {Array} Array of match objects with start and end positions
 */
export const getSearchMatches = (text, searchTerm) => {
  if (!text || !searchTerm) return [];

  const searchWords = searchTerm
    .toLowerCase()
    .trim()
    .split(/\s+/)
    .filter(word => word.length > 0);

  if (searchWords.length === 0) return [];

  const matches = [];
  const lowerText = text.toLowerCase();

  // Find matches for each search word
  searchWords.forEach(word => {
    let startIndex = 0;
    let matchIndex;

    while ((matchIndex = lowerText.indexOf(word, startIndex)) !== -1) {
      matches.push({
        start: matchIndex,
        end: matchIndex + word.length,
        text: text.substring(matchIndex, matchIndex + word.length),
        word: word
      });
      startIndex = matchIndex + 1;
    }
  });

  // Sort matches by start position
  return matches.sort((a, b) => a.start - b.start);
};

/**
 * Splits text into parts with highlighted matches (supports multi-word highlighting)
 * @param {string} text - Text to split
 * @param {string} searchTerm - Search term to highlight (supports multiple words)
 * @returns {Array} Array of text parts with highlight information
 */
export const splitTextWithHighlights = (text, searchTerm) => {
  if (!text || !searchTerm) {
    return [{ text, isHighlight: false }];
  }

  const matches = getSearchMatches(text, searchTerm);

  if (matches.length === 0) {
    return [{ text, isHighlight: false }];
  }

  // Merge overlapping matches
  const mergedMatches = mergeOverlappingMatches(matches);

  const parts = [];
  let lastEnd = 0;

  mergedMatches.forEach(match => {
    // Add text before the match
    if (match.start > lastEnd) {
      parts.push({
        text: text.substring(lastEnd, match.start),
        isHighlight: false
      });
    }

    // Add the highlighted match
    parts.push({
      text: text.substring(match.start, match.end),
      isHighlight: true
    });

    lastEnd = match.end;
  });

  // Add remaining text after the last match
  if (lastEnd < text.length) {
    parts.push({
      text: text.substring(lastEnd),
      isHighlight: false
    });
  }

  return parts;
};

/**
 * Merges overlapping or adjacent matches
 * @param {Array} matches - Array of match objects
 * @returns {Array} Array of merged match objects
 */
const mergeOverlappingMatches = (matches) => {
  if (matches.length <= 1) return matches;

  const sorted = [...matches].sort((a, b) => a.start - b.start);
  const merged = [sorted[0]];

  for (let i = 1; i < sorted.length; i++) {
    const current = sorted[i];
    const last = merged[merged.length - 1];

    // If current match overlaps or is adjacent to the last merged match
    if (current.start <= last.end + 1) {
      // Extend the last merged match
      last.end = Math.max(last.end, current.end);
    } else {
      // Add as a new separate match
      merged.push(current);
    }
  }

  return merged;
};

/**
 * Checks if a product matches the search term in any field (supports multi-word search)
 * @param {Object} product - Product object to check
 * @param {string} searchTerm - Search term to match (supports multiple words)
 * @returns {Object} Object with match status and matched fields
 */
export const getProductSearchMatch = (product, searchTerm) => {
  if (!searchTerm || !searchTerm.trim()) {
    return { matches: false, matchedFields: [], searchWords: [] };
  }

  const searchWords = searchTerm
    .toLowerCase()
    .trim()
    .split(/\s+/)
    .filter(word => word.length > 0);

  if (searchWords.length === 0) {
    return { matches: false, matchedFields: [], searchWords: [] };
  }

  const matchedFields = [];

  // Check each searchable field
  const searchableFields = [
    { key: 'name', value: product.name },
    { key: 'category', value: product.category },
    { key: 'subcategory', value: product.subcategory },
    { key: 'material', value: product.material },
    { key: 'description', value: product.description },
    { key: 'diameter', value: product.diameter },
    { key: 'type', value: product.type },
    { key: 'brand', value: product.brand },
    { key: 'model', value: product.model }
  ];

  // Check if each field contains ALL search words
  searchableFields.forEach(field => {
    if (field.value) {
      const fieldText = field.value.toLowerCase();
      const hasAllWords = searchWords.every(word => fieldText.includes(word));
      if (hasAllWords) {
        matchedFields.push(field.key);
      }
    }
  });

  // Also check if the combined searchable text contains all words
  const searchableText = getProductSearchableText(product);
  const hasAllWordsInCombined = searchWords.every(word => searchableText.includes(word));

  return {
    matches: hasAllWordsInCombined,
    matchedFields,
    searchWords
  };
};

/**
 * Parses search term into individual words
 * @param {string} searchTerm - Search term to parse
 * @returns {Array} Array of individual search words
 */
export const parseSearchWords = (searchTerm) => {
  if (!searchTerm || !searchTerm.trim()) {
    return [];
  }

  return searchTerm
    .toLowerCase()
    .trim()
    .split(/\s+/)
    .filter(word => word.length > 0);
};

/**
 * Checks if text contains any of the search words
 * @param {string} text - Text to search in
 * @param {Array} searchWords - Array of search words
 * @returns {boolean} True if text contains any search word
 */
export const containsAnySearchWord = (text, searchWords) => {
  if (!text || !searchWords || searchWords.length === 0) return false;

  const lowerText = text.toLowerCase();
  return searchWords.some(word => lowerText.includes(word));
};

/**
 * Checks if text contains all search words
 * @param {string} text - Text to search in
 * @param {Array} searchWords - Array of search words
 * @returns {boolean} True if text contains all search words
 */
export const containsAllSearchWords = (text, searchWords) => {
  if (!text || !searchWords || searchWords.length === 0) return false;

  const lowerText = text.toLowerCase();
  return searchWords.every(word => lowerText.includes(word));
};
