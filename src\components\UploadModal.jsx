import React, { useState, useRef, useEffect } from 'react';
import { X, Upload, Camera, Plus } from 'lucide-react';
import { productCategories } from '../data/products';
import { useLanguage } from '../contexts/LanguageContext';
import { useSettings } from '../contexts/SettingsContext';

const UploadModal = ({ onClose, onSubmit }) => {
  const { t, tc } = useLanguage();
  const {
    addCustomCategory,
    addCustomSubcategory,
    getAllCategories,
    ensureCategoryExists,
    customProducts,
    updateCustomProduct
  } = useSettings();
  const [formData, setFormData] = useState({
    name: '',
    category: '',
    subcategory: '',
    price: '0.00',
    diameter: 'N/A',
    material: 'Standard',
    image: null
  });
  const [imagePreview, setImagePreview] = useState(null);
  const [errors, setErrors] = useState({});

  // Category creation state
  const [showAddCategory, setShowAddCategory] = useState(false);
  const [showAddSubcategory, setShowAddSubcategory] = useState(false);
  const [newCategoryName, setNewCategoryName] = useState('');
  const [newSubcategoryName, setNewSubcategoryName] = useState('');
  const [newSubcategoryParent, setNewSubcategoryParent] = useState('');
  const [categoryErrors, setCategoryErrors] = useState({});

  // Camera functionality state
  const [showCameraStream, setShowCameraStream] = useState(false);
  const [isProcessingImage, setIsProcessingImage] = useState(false);
  const [isCameraReady, setIsCameraReady] = useState(false);
  const [showCameraReadyIndicator, setShowCameraReadyIndicator] = useState(false);
  const videoRef = useRef(null);
  const canvasRef = useRef(null);
  const streamRef = useRef(null);

  // URL image loading state
  const [urlInput, setUrlInput] = useState('');
  const [isLoadingUrl, setIsLoadingUrl] = useState(false);
  const [urlValidationStatus, setUrlValidationStatus] = useState(''); // 'valid', 'invalid', 'loading', ''
  const urlTimeoutRef = useRef(null);

  // Duplicate detection state
  const [showDuplicateDialog, setShowDuplicateDialog] = useState(false);
  const [duplicateProduct, setDuplicateProduct] = useState(null);
  const [pendingProductData, setPendingProductData] = useState(null);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const handleImageUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      if (file.type.startsWith('image/')) {
        // Validate file size (max 5MB for better performance)
        if (file.size > 5 * 1024 * 1024) {
          setErrors(prev => ({
            ...prev,
            image: t('imageTooLarge') || 'Image file is too large (max 5MB)'
          }));
          return;
        }

        // Validate file type more strictly
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
        if (!allowedTypes.includes(file.type)) {
          setErrors(prev => ({
            ...prev,
            image: t('unsupportedImageFormat') || 'Unsupported image format. Please use JPG, PNG, GIF, or WebP.'
          }));
          return;
        }

        const reader = new FileReader();
        reader.onload = (e) => {
          const imageData = e.target.result;
          setFormData(prev => ({
            ...prev,
            image: imageData
          }));
          setImagePreview(imageData);
          // Clear any previous errors
          setErrors(prev => ({
            ...prev,
            image: ''
          }));
        };

        reader.onerror = () => {
          setErrors(prev => ({
            ...prev,
            image: t('errorProcessingImage') || 'Error processing image file'
          }));
        };

        reader.readAsDataURL(file);
      } else {
        setErrors(prev => ({
          ...prev,
          image: t('invalidImageFile') || 'Please select a valid image file'
        }));
      }
    }

    // Reset the input value to allow selecting the same file again
    e.target.value = '';
  };

  // Enhanced URL validation function
  const validateImageUrl = (url) => {
    if (!url) return false;

    // Check for data URLs
    if (url.startsWith('data:image/')) return true;

    // Enhanced URL pattern that supports more formats
    const urlPattern = /^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/i;
    if (!urlPattern.test(url)) return false;

    // Check for common image file extensions
    const imageExtensions = /\.(jpg|jpeg|png|gif|webp|svg|bmp|ico)(\?.*)?$/i;
    const hasImageExtension = imageExtensions.test(url);

    // Allow URLs without extensions if they look like image URLs (e.g., from CDNs)
    const looksLikeImageUrl = /\/(image|img|photo|picture|avatar|thumbnail)/i.test(url) ||
                             /\b(unsplash|imgur|cloudinary|amazonaws|googleusercontent)\b/i.test(url);

    return hasImageExtension || looksLikeImageUrl;
  };

  // Real-time URL validation with debouncing
  const handleUrlInputChange = (value) => {
    setUrlInput(value);

    // Clear previous timeout
    if (urlTimeoutRef.current) {
      clearTimeout(urlTimeoutRef.current);
    }

    // Reset validation status
    setUrlValidationStatus('');
    setErrors(prev => ({ ...prev, image: '' }));

    if (!value.trim()) {
      return;
    }

    // Debounce validation
    urlTimeoutRef.current = setTimeout(() => {
      const trimmedUrl = value.trim();

      if (validateImageUrl(trimmedUrl)) {
        setUrlValidationStatus('valid');
      } else {
        setUrlValidationStatus('invalid');
        setErrors(prev => ({
          ...prev,
          image: t('invalidImageUrl') || 'Please enter a valid image URL (e.g., https://example.com/image.jpg)'
        }));
      }
    }, 500); // 500ms debounce
  };

  const handleImageUrl = (url) => {
    const trimmedUrl = url.trim();
    if (!trimmedUrl) return;

    // Clear URL input
    setUrlInput('');
    setUrlValidationStatus('');

    // Validate URL format
    if (!validateImageUrl(trimmedUrl)) {
      setErrors(prev => ({
        ...prev,
        image: t('invalidImageUrl') || 'Please enter a valid image URL (e.g., https://example.com/image.jpg)'
      }));
      return;
    }

    // Ensure URL has protocol for external URLs
    let finalUrl = trimmedUrl;
    const isDataUrl = trimmedUrl.startsWith('data:image/');

    if (!isDataUrl && !trimmedUrl.startsWith('http://') && !trimmedUrl.startsWith('https://')) {
      finalUrl = 'https://' + trimmedUrl;
    }

    // Set loading state
    setIsLoadingUrl(true);
    setErrors(prev => ({ ...prev, image: '' }));

    // If it's an HTTP URL, test loading
    if (finalUrl.startsWith('http')) {
      const testImage = new Image();

      // Set a timeout for the image loading
      const timeout = setTimeout(() => {
        setIsLoadingUrl(false);
        setErrors(prev => ({
          ...prev,
          image: t('imageLoadTimeout') || 'Image loading timed out. Please check the URL.'
        }));
      }, 10000); // 10 second timeout

      testImage.onload = () => {
        clearTimeout(timeout);
        setIsLoadingUrl(false);

        // Validate image dimensions
        if (testImage.width === 0 || testImage.height === 0) {
          setErrors(prev => ({
            ...prev,
            image: t('invalidImageFile') || 'Invalid image file. Please check the URL.'
          }));
          return;
        }

        // Validate reasonable image size (not too large)
        if (testImage.width > 5000 || testImage.height > 5000) {
          setErrors(prev => ({
            ...prev,
            image: t('imageTooLarge') || 'Image is too large. Please use an image smaller than 5000x5000 pixels.'
          }));
          return;
        }

        // Success - set the image
        setFormData(prev => ({
          ...prev,
          image: finalUrl
        }));
        setImagePreview(finalUrl);
        setErrors(prev => ({
          ...prev,
          image: ''
        }));
      };

      testImage.onerror = () => {
        clearTimeout(timeout);
        setIsLoadingUrl(false);
        setErrors(prev => ({
          ...prev,
          image: t('imageLoadFailed') || 'Failed to load image from URL. Please check the URL and try again.'
        }));
      };

      // Set crossOrigin to handle CORS issues
      testImage.crossOrigin = 'anonymous';
      testImage.src = finalUrl;
    } else {
      // For data URLs, just set directly
      setIsLoadingUrl(false);
      setFormData(prev => ({
        ...prev,
        image: finalUrl
      }));
      setImagePreview(finalUrl);
      setErrors(prev => ({
        ...prev,
        image: ''
      }));
    }
  };

  // Camera functionality
  const startCameraStream = async () => {
    try {
      setErrors(prev => ({ ...prev, image: '' }));
      setIsProcessingImage(true);
      setIsCameraReady(false); // Reset camera ready state

      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        throw new Error('Camera not supported on this device');
      }

      let stream = null;
      const constraints = [
        {
          video: {
            width: { ideal: 1280 },
            height: { ideal: 720 },
            facingMode: { exact: 'environment' }
          }
        },
        {
          video: {
            width: { ideal: 1280 },
            height: { ideal: 720 },
            facingMode: 'user'
          }
        },
        { video: true }
      ];

      for (const constraint of constraints) {
        try {
          stream = await navigator.mediaDevices.getUserMedia(constraint);
          break;
        } catch (err) {
          continue;
        }
      }

      if (!stream) {
        throw new Error('Unable to access any camera');
      }

      streamRef.current = stream;
      console.log(`UploadModal - Video ref status: ${videoRef.current ? 'Available' : 'NULL'}`);

      // Set camera stream to active first to render the video element
      setShowCameraStream(true);

      // Wait a moment for the video element to be rendered
      await new Promise(resolve => setTimeout(resolve, 100));

      console.log(`UploadModal - Video ref status after delay: ${videoRef.current ? 'Available' : 'NULL'}`);

      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        console.log('UploadModal - Video srcObject set, starting enhanced readiness detection...');

        // Enhanced approach: Start video playback immediately and monitor readiness
        const video = videoRef.current;

        // Start playing the video immediately
        try {
          await video.play();
          console.log('UploadModal - Video play() called successfully');
        } catch (playError) {
          console.log('UploadModal - Video play() failed:', playError.message, ', continuing with readiness checks...');
        }

        // Enhanced readiness detection with multiple strategies
        await new Promise((resolve, reject) => {
          let resolved = false;
          let checkCount = 0;
          const maxChecks = 100; // 10 seconds total

          const checkVideoReadiness = () => {
            if (resolved) return;
            checkCount++;

            const readyState = video.readyState;
            const width = video.videoWidth;
            const height = video.videoHeight;
            const paused = video.paused;
            const ended = video.ended;

            console.log(`UploadModal - Check #${checkCount}: readyState=${readyState}, width=${width}, height=${height}, paused=${paused}, ended=${ended}`);

            // Multiple readiness criteria - any of these should work
            const isReady = (
              // Primary criteria: full readiness
              (readyState >= 2 && width > 0 && height > 0 && !paused && !ended) ||
              // Secondary criteria: has dimensions and some data
              (readyState >= 1 && width > 0 && height > 0) ||
              // Fallback criteria: just has dimensions (for some browsers)
              (width > 0 && height > 0 && checkCount > 10)
            );

            if (isReady) {
              resolved = true;
              console.log(`UploadModal - Camera is ready! Criteria met: readyState=${readyState}, dimensions=${width}x${height}`);
              setIsCameraReady(true);
              setShowCameraReadyIndicator(true);

              setTimeout(() => {
                setShowCameraReadyIndicator(false);
              }, 3000);

              resolve();
            } else if (checkCount >= maxChecks) {
              // Final timeout - force resolution if video has any dimensions
              if (width > 0 && height > 0) {
                console.log(`UploadModal - Forcing camera ready after ${maxChecks} checks - video has dimensions ${width}x${height}`);
                resolved = true;
                setIsCameraReady(true);
                setShowCameraReadyIndicator(true);
                setTimeout(() => setShowCameraReadyIndicator(false), 3000);
                resolve();
              } else {
                console.log(`UploadModal - Camera readiness failed after ${maxChecks} checks - no video dimensions`);
                reject(new Error('Camera readiness timeout - no video dimensions detected'));
              }
            } else {
              // Continue checking
              setTimeout(checkVideoReadiness, 100);
            }
          };

          // Start checking immediately
          checkVideoReadiness();

          // Additional event listeners as backup
          const onLoadedData = () => {
            if (!resolved) {
              console.log('UploadModal - loadeddata event fired, triggering readiness check');
              checkVideoReadiness();
            }
          };

          const onCanPlay = () => {
            if (!resolved) {
              console.log('UploadModal - canplay event fired, triggering readiness check');
              checkVideoReadiness();
            }
          };

          const onLoadedMetadata = () => {
            if (!resolved) {
              console.log('UploadModal - loadedmetadata event fired, triggering readiness check');
              checkVideoReadiness();
            }
          };

          const onError = (error) => {
            if (!resolved) {
              resolved = true;
              console.log('UploadModal - Video error:', error.message || error);
              reject(error);
            }
          };

          video.addEventListener('loadeddata', onLoadedData);
          video.addEventListener('canplay', onCanPlay);
          video.addEventListener('loadedmetadata', onLoadedMetadata);
          video.addEventListener('error', onError);

          // Cleanup function
          const cleanup = () => {
            video.removeEventListener('loadeddata', onLoadedData);
            video.removeEventListener('canplay', onCanPlay);
            video.removeEventListener('loadedmetadata', onLoadedMetadata);
            video.removeEventListener('error', onError);
          };

          // Final timeout
          setTimeout(() => {
            if (!resolved) {
              cleanup();
              console.log('UploadModal - Final timeout reached - forcing resolution if possible');
              if (video.videoWidth > 0 && video.videoHeight > 0) {
                resolved = true;
                setIsCameraReady(true);
                setShowCameraReadyIndicator(true);
                setTimeout(() => setShowCameraReadyIndicator(false), 3000);
                resolve();
              } else {
                reject(new Error('Video loading timeout - no dimensions after 15 seconds'));
              }
            } else {
              cleanup();
            }
          }, 15000);
        });
      } else {
        console.log('UploadModal - ERROR: Video element not available even after delay!');
      }

      setIsProcessingImage(false);
    } catch (error) {
      console.error('UploadModal - Error accessing camera:', error);
      setIsProcessingImage(false);
      setIsCameraReady(false); // Ensure camera ready state is reset on error
      setShowCameraStream(false); // Hide camera stream on error

      let errorMessage = 'Unable to access camera';

      if (error.name === 'NotAllowedError') {
        errorMessage = 'Camera access denied. Please allow camera access and try again.';
      } else if (error.name === 'NotFoundError') {
        errorMessage = 'No camera found on this device.';
      } else if (error.name === 'NotSupportedError' || error.message.includes('not supported')) {
        errorMessage = 'Camera not supported on this device.';
      } else if (error.message.includes('timeout')) {
        errorMessage = 'Camera initialization timed out. Please try again.';
      }

      setErrors(prev => ({ ...prev, image: errorMessage }));
    }
  };

  const stopCameraStream = () => {
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
      streamRef.current = null;
    }
    setShowCameraStream(false);
    setIsCameraReady(false);
    setShowCameraReadyIndicator(false);
  };

  const capturePhoto = () => {
    if (!videoRef.current || !canvasRef.current) {
      setErrors(prev => ({ ...prev, image: 'Camera not ready' }));
      return;
    }

    try {
      const video = videoRef.current;
      const canvas = canvasRef.current;
      const context = canvas.getContext('2d');

      // Enhanced video readiness check
      const checkVideoReady = () => {
        return video.readyState >= 2 && // HAVE_CURRENT_DATA or higher
               video.videoWidth > 0 &&
               video.videoHeight > 0 &&
               !video.paused &&
               !video.ended;
      };

      // If video is not ready, wait a bit and try again
      if (!checkVideoReady()) {
        setErrors(prev => ({ ...prev, image: 'Preparing camera...' }));

        // Wait for video to be ready with timeout
        const waitForVideo = new Promise((resolve, reject) => {
          let attempts = 0;
          const maxAttempts = 20; // 2 seconds total

          const checkInterval = setInterval(() => {
            attempts++;

            if (checkVideoReady()) {
              clearInterval(checkInterval);
              resolve();
            } else if (attempts >= maxAttempts) {
              clearInterval(checkInterval);
              reject(new Error('Video not ready after timeout'));
            }
          }, 100); // Check every 100ms
        });

        waitForVideo
          .then(() => {
            // Clear the preparing message and retry capture
            setErrors(prev => ({ ...prev, image: '' }));
            setTimeout(() => capturePhoto(), 100);
          })
          .catch(() => {
            setErrors(prev => ({ ...prev, image: 'Camera not responding. Please try restarting the camera.' }));
          });

        return;
      }

      // Get actual video dimensions
      const videoWidth = video.videoWidth || video.clientWidth || 640;
      const videoHeight = video.videoHeight || video.clientHeight || 480;

      // Set canvas dimensions to match video
      canvas.width = videoWidth;
      canvas.height = videoHeight;

      // Clear canvas first
      context.clearRect(0, 0, canvas.width, canvas.height);

      // Draw the video frame to canvas
      context.drawImage(video, 0, 0, canvas.width, canvas.height);

      // Convert to base64 with good quality
      const base64Data = canvas.toDataURL('image/jpeg', 0.85);

      // Validate the captured image
      if (base64Data === 'data:,' || base64Data.length < 1000) {
        setErrors(prev => ({ ...prev, image: 'Failed to capture photo. Please try again.' }));
        return;
      }

      setFormData(prev => ({ ...prev, image: base64Data }));
      setImagePreview(base64Data);
      stopCameraStream();
      setErrors(prev => ({ ...prev, image: '' }));
    } catch (error) {
      console.error('Error capturing photo:', error);
      setErrors(prev => ({ ...prev, image: 'Failed to capture photo' }));
    }
  };

  useEffect(() => {
    return () => {
      stopCameraStream();
      // Cleanup URL validation timeout
      if (urlTimeoutRef.current) {
        clearTimeout(urlTimeoutRef.current);
      }
    };
  }, []);

  const validateForm = () => {
    const newErrors = {};

    // Only validate required fields
    if (!formData.name.trim()) newErrors.name = t('productNameRequired');
    if (!formData.category) newErrors.category = t('categoryRequired');
    if (!formData.subcategory) newErrors.subcategory = t('subcategoryRequired');

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Check for duplicate products
  const findDuplicateProduct = (name, category, subcategory) => {
    return customProducts.find(product =>
      product.name.toLowerCase() === name.toLowerCase() &&
      product.category.toLowerCase() === category.toLowerCase() &&
      product.subcategory.toLowerCase() === subcategory.toLowerCase()
    );
  };

  // Handle duplicate resolution
  const handleDuplicateResolution = (action) => {
    if (!pendingProductData || !duplicateProduct) return;

    switch (action) {
      case 'replace':
        // Update the existing product with new data
        updateCustomProduct(duplicateProduct.id, {
          ...duplicateProduct,
          ...pendingProductData,
          updatedAt: new Date().toISOString()
        });

        // Show success message and close modal
        alert(t('Product replaced successfully!') || 'Product replaced successfully!');
        onClose();
        break;

      case 'modify':
        // Keep the form open for user to modify
        setShowDuplicateDialog(false);
        setDuplicateProduct(null);
        setPendingProductData(null);
        // Form stays open with current data
        break;

      case 'cancel':
        // Cancel the operation
        setShowDuplicateDialog(false);
        setDuplicateProduct(null);
        setPendingProductData(null);
        break;

      default:
        break;
    }
  };

  // Category management functions
  const handleAddCategory = () => {
    const trimmedName = newCategoryName.trim();
    if (!trimmedName) {
      setCategoryErrors({ category: t('categoryNameRequired') });
      return;
    }

    const allCategories = getAllCategories();
    const existingCategory = allCategories.find(cat =>
      cat.name.toLowerCase() === trimmedName.toLowerCase()
    );

    if (existingCategory) {
      setCategoryErrors({ category: t('categoryNameExists') });
      return;
    }

    addCustomCategory(trimmedName);
    setFormData(prev => ({ ...prev, category: trimmedName, subcategory: '' }));
    setNewCategoryName('');
    setShowAddCategory(false);
    setCategoryErrors({});

    // Show success message (you could also pass this up to parent component)
    console.log(t('categoryCreatedSuccessfully') || 'Category created successfully');
  };

  const handleAddSubcategory = () => {
    const trimmedName = newSubcategoryName.trim();
    const trimmedParent = newSubcategoryParent.trim();

    if (!trimmedName) {
      setCategoryErrors({ subcategory: t('subcategoryNameRequired') });
      return;
    }

    if (!trimmedParent) {
      setCategoryErrors({ parent: t('parentCategoryRequired') });
      return;
    }

    const allCategories = getAllCategories();
    const parentCategory = allCategories.find(cat => cat.name === trimmedParent);

    if (parentCategory && parentCategory.subcategories.includes(trimmedName)) {
      setCategoryErrors({ subcategory: t('subcategoryNameExists') });
      return;
    }

    addCustomSubcategory(trimmedParent, trimmedName);
    setFormData(prev => ({
      ...prev,
      category: trimmedParent,
      subcategory: trimmedName
    }));
    setNewSubcategoryName('');
    setNewSubcategoryParent('');
    setShowAddSubcategory(false);
    setCategoryErrors({});

    // Show success message (you could also pass this up to parent component)
    console.log(t('subcategoryCreatedSuccessfully') || 'Subcategory created successfully');
  };

  const handleSubmit = (e) => {
    e.preventDefault();

    if (validateForm()) {
      // Simple placeholder image for better compatibility
      const simplePlaceholder = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADwAAAA8CAYAAAA6/NlyAAAACXBIWXMAAAsTAAALEwEAmpwYAAABaUlEQVR4nO2ZQU7DMBBFXwVLWkcsuuAQ5VBcAg7DnlP0ArRCUJZVd43VLmpIadLSJk7nw8hSpKjJ/PmbsT2WCSGEEEIIIcT/xMzOgAfgE1hHjUPHQQwzuwQWUeM99gOsXG+0ALvAMgr4rQXuADaI8GoL8bDYB+DEzGbAGZDHnwSrZgTYoKE1t0+Mq2YE2MxugPdU1cwWsIXf85zx4RXn3H13DQ36HA7xeX7n95zzK+p0DJoSXgA3hYuGBmXCOXe9efQU8kDGK78GHmP7GrQ5TGpwfz4wKOElcJuq2dB+QnnlIcDKGCcsYYHzyk9Q0LS1qKrZgQrLK78F1r4HDWjO7wZ4SdVszz9mD3teeRNY4By4T9Us1qBlUVUGHaMdqLDyyntgfagfOsYWVJ6YYUm4BgzxOet55edUzVJwrI6hyjjhfSisZx/+yjGEbMJZTICvHHMb4E/3wpTxDHxLWAghhBBCCCEO4xu8YT7UwwFdnwAAAABJRU5ErkJggg==';

      // Create a default placeholder image if none provided
      const defaultImage = formData.image || simplePlaceholder;

      // Validate that the image is a valid data URL with extra safety
      const isValidImage = typeof defaultImage === 'string' &&
        (defaultImage.startsWith('data:image/') || defaultImage.startsWith('http'));

      // Use valid image or fallback to a simple placeholder
      const fallbackImage = simplePlaceholder;
      const finalImage = isValidImage ? defaultImage : fallbackImage;

      // Prepare product data with proper category/subcategory association
      const productData = {
        ...formData,
        name: formData.name.trim(),
        price: parseFloat(formData.price) || 0,
        diameter: formData.diameter || 'N/A',
        material: formData.material || 'Standard',
        image: finalImage,
        // Ensure category and subcategory are properly set
        category: formData.category.trim(),
        subcategory: formData.subcategory.trim()
      };

      // Console log the image for debugging
      console.log('Product image data:', finalImage.substring(0, 50) + '...');

      // Check for duplicate products
      const duplicate = findDuplicateProduct(
        productData.name,
        productData.category,
        productData.subcategory
      );

      if (duplicate) {
        // Show duplicate dialog
        setDuplicateProduct(duplicate);
        setPendingProductData(productData);
        setShowDuplicateDialog(true);
        return;
      }

      // No duplicate found, proceed with normal submission
      // Check if we're creating new categories/subcategories
      const allCategories = getAllCategories();
      const existingCategory = allCategories.find(cat => cat.name === productData.category);
      const isNewCategory = !existingCategory;
      const isNewSubcategory = existingCategory && !existingCategory.subcategories.includes(productData.subcategory);

      // Submit the product (this will auto-create categories/subcategories if needed)
      onSubmit(productData);

      // Show appropriate success message
      let message = `Product "${productData.name}" added successfully!`;
      if (isNewCategory) {
        message += ` New category "${productData.category}" created.`;
      } else if (isNewSubcategory) {
        message += ` New subcategory "${productData.subcategory}" added to "${productData.category}".`;
      }

      console.log(message); // This could be passed to parent for display
    }
  };

  // Get all categories including custom ones
  const allCategories = getAllCategories();
  const selectedCategory = allCategories.find(cat => cat.name === formData.category);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-dark-800 rounded-lg shadow-xl w-full max-w-md max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-dark-700">
          <h2 className="text-xl font-semibold text-white">{t('addNewProduct')}</h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-dark-700 rounded-lg transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          {/* Product Name */}
          <div>
            <label className="block text-sm font-medium text-dark-200 mb-2">
              {t('productName')} *
            </label>
            <input
              type="text"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-lg text-white focus:outline-none focus:border-primary-500"
              placeholder={t('enterProductName')}
            />
            {errors.name && <p className="text-red-400 text-xs mt-1">{errors.name}</p>}
          </div>

          {/* Category */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <label className="block text-sm font-medium text-dark-200">
                {t('category')} *
              </label>
              <button
                type="button"
                onClick={() => setShowAddCategory(true)}
                className="flex items-center gap-1 px-2 py-1 text-xs bg-primary-600 hover:bg-primary-700 rounded text-white transition-colors"
              >
                <Plus className="w-3 h-3" />
                {t('addNewCategory')}
              </button>
            </div>
            <select
              name="category"
              value={formData.category}
              onChange={handleInputChange}
              className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-lg text-white focus:outline-none focus:border-primary-500"
            >
              <option value="">{t('selectCategory')}</option>
              {allCategories.map(category => (
                <option key={category.name} value={category.name}>
                  {tc(category.name)}
                </option>
              ))}
            </select>
            {errors.category && <p className="text-red-400 text-xs mt-1">{errors.category}</p>}
          </div>

          {/* Subcategory */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <label className="block text-sm font-medium text-dark-200">
                {t('subcategory')} *
              </label>
              <button
                type="button"
                onClick={() => {
                  setNewSubcategoryParent(formData.category);
                  setShowAddSubcategory(true);
                }}
                disabled={!formData.category}
                className="flex items-center gap-1 px-2 py-1 text-xs bg-primary-600 hover:bg-primary-700 rounded text-white transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <Plus className="w-3 h-3" />
                {t('addNewSubcategory')}
              </button>
            </div>
            <select
              name="subcategory"
              value={formData.subcategory}
              onChange={handleInputChange}
              disabled={!selectedCategory}
              className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-lg text-white focus:outline-none focus:border-primary-500 disabled:opacity-50"
            >
              <option value="">{t('selectSubcategory')}</option>
              {selectedCategory?.subcategories.map(subcategory => (
                <option key={subcategory} value={subcategory}>
                  {subcategory}
                </option>
              ))}
            </select>
            {errors.subcategory && <p className="text-red-400 text-xs mt-1">{errors.subcategory}</p>}
          </div>

          {/* Price and Diameter - Optional */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-dark-200 mb-2">
                {t('price')}
              </label>
              <input
                type="number"
                name="price"
                value={formData.price}
                onChange={handleInputChange}
                step="0.01"
                min="0"
                className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-lg text-white focus:outline-none focus:border-primary-500"
                placeholder="0.00"
              />
              <p className="text-dark-400 text-xs mt-1">{t('optional') || 'Optional'}</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-dark-200 mb-2">
                {t('diameter')}
              </label>
              <input
                type="text"
                name="diameter"
                value={formData.diameter}
                onChange={handleInputChange}
                className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-lg text-white focus:outline-none focus:border-primary-500"
                placeholder="N/A"
              />
              <p className="text-dark-400 text-xs mt-1">{t('optional') || 'Optional'}</p>
            </div>
          </div>

          {/* Material - Optional */}
          <div>
            <label className="block text-sm font-medium text-dark-200 mb-2">
              {t('material')}
            </label>
            <input
              type="text"
              name="material"
              value={formData.material}
              onChange={handleInputChange}
              className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-lg text-white focus:outline-none focus:border-primary-500"
              placeholder="Standard"
            />
            <p className="text-dark-400 text-xs mt-1">{t('optional') || 'Optional'}</p>
          </div>

          {/* Image Upload - Optional */}
          <div>
            <label className="block text-sm font-medium text-dark-200 mb-2">
              {t('productImage')}
            </label>
            <p className="text-dark-400 text-xs mb-2">{t('optional') || 'Optional'} - {t('defaultImageWillBeUsed') || 'A default placeholder will be used if no image is provided'}</p>

            <div className="space-y-3">
              {/* Image Error Display */}
              {errors.image && (
                <div className="p-3 rounded-lg bg-red-900/20 border border-red-500/30 text-red-400">
                  <p className="text-sm font-medium">{errors.image}</p>
                </div>
              )}

              {/* Enhanced Image Preview */}
              {imagePreview && (
                <div className="relative group">
                  <div className="relative bg-gray-100 rounded-lg border border-dark-600 overflow-hidden">
                    <img
                      src={imagePreview}
                      alt="Preview"
                      className="w-full h-40 object-contain bg-gradient-to-br from-gray-50 to-gray-100"
                      onLoad={(e) => {
                        // Log successful image load
                        console.log('Image preview loaded successfully:', {
                          width: e.target.naturalWidth,
                          height: e.target.naturalHeight,
                          src: imagePreview.substring(0, 50) + '...'
                        });
                      }}
                      onError={(e) => {
                        console.error('Image preview failed to load:', imagePreview);
                        setErrors(prev => ({
                          ...prev,
                          image: 'Failed to display image preview. The image may be corrupted or inaccessible.'
                        }));
                        setImagePreview(null);
                        setFormData(prev => ({ ...prev, image: null }));
                      }}
                    />

                    {/* Image info overlay */}
                    <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-70 text-white text-xs p-2 opacity-0 group-hover:opacity-100 transition-opacity">
                      <div className="flex justify-between items-center">
                        <span>Image loaded successfully</span>
                        <span className="text-green-300">✓</span>
                      </div>
                    </div>
                  </div>

                  {/* Remove button */}
                  <button
                    type="button"
                    onClick={() => {
                      setImagePreview(null);
                      setFormData(prev => ({ ...prev, image: null }));
                      setUrlInput('');
                      setUrlValidationStatus('');
                      setErrors(prev => ({ ...prev, image: '' }));
                    }}
                    className="absolute -top-2 -right-2 w-7 h-7 bg-red-500 hover:bg-red-600 rounded-full flex items-center justify-center text-white text-sm font-bold shadow-lg transition-colors"
                    title="Remove image"
                  >
                    ×
                  </button>
                </div>
              )}

              {/* Upload Options */}
              {!imagePreview && (
                <>
                  <label className="flex items-center justify-center w-full h-32 border-2 border-dashed border-dark-600 rounded-lg cursor-pointer hover:border-primary-500 transition-colors">
                    <div className="text-center">
                      <Upload className="w-8 h-8 mx-auto mb-2 text-dark-400" />
                      <p className="text-sm text-dark-400">{t('clickToUpload') || 'Click to upload'}</p>
                      <p className="text-xs text-dark-500 mt-1">{t('supportedFormats') || 'JPG, PNG, GIF up to 10MB'}</p>
                    </div>
                    <input
                      type="file"
                      accept="image/*"
                      onChange={handleImageUpload}
                      className="hidden"
                    />
                  </label>

                  {/* Enhanced Camera option */}
                  {!showCameraStream ? (
                    <button
                      type="button"
                      onClick={startCameraStream}
                      disabled={isProcessingImage}
                      className={`flex items-center justify-center w-full py-2 border border-dark-600 rounded-lg transition-colors ${
                        isProcessingImage
                          ? 'cursor-not-allowed opacity-50'
                          : 'cursor-pointer hover:border-primary-500'
                      }`}
                    >
                      <Camera className="w-4 h-4 mr-2" />
                      <span className="text-sm">
                        {isProcessingImage ? t('Starting camera...') || 'Starting camera...' : t('takePhoto') || 'Take Photo'}
                      </span>
                    </button>
                  ) : (
                    <div className="space-y-3">
                      <div className="relative bg-black rounded-lg overflow-hidden">
                        <video
                          ref={videoRef}
                          autoPlay
                          playsInline
                          className="w-full h-48 object-cover"
                        />
                        <div className="absolute bottom-3 left-1/2 transform -translate-x-1/2 flex space-x-3">
                          <button
                            type="button"
                            onClick={capturePhoto}
                            disabled={!isCameraReady}
                            className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                              isCameraReady
                                ? 'bg-white text-gray-900 hover:bg-gray-100'
                                : 'bg-gray-400 text-gray-600 cursor-not-allowed'
                            }`}
                          >
                            <Camera size={14} className="inline mr-1" />
                            {isCameraReady ? t('Capture') || 'Capture' : t('Preparing...') || 'Preparing...'}
                          </button>
                          <button
                            type="button"
                            onClick={stopCameraStream}
                            className="px-3 py-2 bg-red-600 text-white rounded-lg text-sm font-medium hover:bg-red-700 transition-colors"
                          >
                            <X size={14} className="inline mr-1" />
                            {t('Cancel') || 'Cancel'}
                          </button>
                        </div>

                        {/* Camera status indicator */}
                        {!isCameraReady && (
                          <div className="absolute top-3 left-1/2 transform -translate-x-1/2">
                            <div className="bg-black bg-opacity-70 text-white px-3 py-1 rounded-lg text-sm flex items-center">
                              <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-2"></div>
                              {t('Initializing camera...') || 'Initializing camera...'}
                            </div>
                          </div>
                        )}

                        {/* Camera ready indicator */}
                        {isCameraReady && showCameraReadyIndicator && (
                          <div className="absolute top-3 left-1/2 transform -translate-x-1/2">
                            <div className="bg-green-600 bg-opacity-90 text-white px-3 py-1 rounded-lg text-sm flex items-center">
                              <div className="w-2 h-2 bg-green-300 rounded-full mr-2"></div>
                              {t('Camera ready') || 'Camera ready'}
                            </div>
                          </div>
                        )}
                      </div>
                      <canvas ref={canvasRef} className="hidden" />
                    </div>
                  )}

                  {/* Enhanced URL Input */}
                  <div className="relative">
                    <div className="flex">
                      <div className="relative flex-1">
                        <input
                          type="url"
                          value={urlInput}
                          onChange={(e) => handleUrlInputChange(e.target.value)}
                          placeholder="Enter image URL (https://example.com/image.jpg)"
                          className={`w-full px-3 py-2 bg-dark-700 border rounded-l-lg text-white focus:outline-none transition-colors ${
                            urlValidationStatus === 'valid'
                              ? 'border-green-500 focus:border-green-400'
                              : urlValidationStatus === 'invalid'
                              ? 'border-red-500 focus:border-red-400'
                              : 'border-dark-600 focus:border-primary-500'
                          }`}
                          id="imageUrlInput"
                          disabled={isLoadingUrl}
                          onKeyPress={(e) => {
                            if (e.key === 'Enter') {
                              e.preventDefault();
                              if (urlInput.trim() && urlValidationStatus === 'valid') {
                                handleImageUrl(urlInput);
                              }
                            }
                          }}
                        />

                        {/* Validation indicator */}
                        {urlInput && (
                          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                            {urlValidationStatus === 'valid' && (
                              <div className="w-4 h-4 rounded-full bg-green-500 flex items-center justify-center">
                                <svg className="w-2.5 h-2.5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                </svg>
                              </div>
                            )}
                            {urlValidationStatus === 'invalid' && (
                              <div className="w-4 h-4 rounded-full bg-red-500 flex items-center justify-center">
                                <svg className="w-2.5 h-2.5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                  <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                                </svg>
                              </div>
                            )}
                          </div>
                        )}
                      </div>

                      <button
                        type="button"
                        onClick={() => {
                          if (urlInput.trim() && urlValidationStatus === 'valid') {
                            handleImageUrl(urlInput);
                          }
                        }}
                        disabled={!urlInput.trim() || urlValidationStatus !== 'valid' || isLoadingUrl}
                        className={`px-4 py-2 rounded-r-lg font-medium transition-colors ${
                          urlInput.trim() && urlValidationStatus === 'valid' && !isLoadingUrl
                            ? 'bg-primary-600 hover:bg-primary-700 text-white'
                            : 'bg-dark-600 text-dark-400 cursor-not-allowed'
                        }`}
                      >
                        {isLoadingUrl ? (
                          <div className="flex items-center space-x-2">
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                            <span>Loading...</span>
                          </div>
                        ) : (
                          t('add') || 'Add'
                        )}
                      </button>
                    </div>

                    {/* Enhanced help text */}
                    <div className="text-xs mt-1">
                      {urlValidationStatus === 'valid' && (
                        <span className="text-green-400">✓ Valid image URL detected</span>
                      )}
                      {urlValidationStatus === 'invalid' && (
                        <span className="text-red-400">✗ Invalid URL format</span>
                      )}
                      {!urlInput && (
                        <span className="text-dark-400">
                          Supports: JPG, PNG, GIF, WebP, SVG • Direct links from Unsplash, Imgur, etc.
                        </span>
                      )}
                      {isLoadingUrl && (
                        <span className="text-blue-400">🔄 Loading image from URL...</span>
                      )}
                    </div>
                  </div>
                </>
              )}
            </div>
          </div>

          {/* Buttons */}
          <div className="flex gap-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-2 border border-dark-600 rounded-lg text-dark-200 hover:bg-dark-700 transition-colors"
            >
              {t('cancel')}
            </button>
            <button
              type="submit"
              className="flex-1 px-4 py-2 bg-primary-600 hover:bg-primary-700 rounded-lg text-white transition-colors"
            >
              {t('addProduct')}
            </button>
          </div>
        </form>
      </div>

      {/* Add New Category Modal */}
      {showAddCategory && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-60">
          <div className="bg-dark-800 rounded-lg shadow-xl w-full max-w-sm mx-4">
            <div className="flex items-center justify-between p-4 border-b border-dark-700">
              <h3 className="text-lg font-semibold text-white">{t('addNewCategory')}</h3>
              <button
                onClick={() => {
                  setShowAddCategory(false);
                  setNewCategoryName('');
                  setCategoryErrors({});
                }}
                className="p-1 hover:bg-dark-700 rounded transition-colors"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
            <div className="p-4">
              <div className="mb-4">
                <label className="block text-sm font-medium text-dark-200 mb-2">
                  {t('categoryName')} *
                </label>
                <input
                  type="text"
                  value={newCategoryName}
                  onChange={(e) => {
                    setNewCategoryName(e.target.value);
                    if (categoryErrors.category) {
                      setCategoryErrors(prev => ({ ...prev, category: '' }));
                    }
                  }}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-lg text-white focus:outline-none focus:border-primary-500"
                  placeholder={t('enterCategoryName')}
                  autoFocus
                />
                {categoryErrors.category && (
                  <p className="text-red-400 text-xs mt-1">{categoryErrors.category}</p>
                )}
              </div>
              <div className="flex gap-3">
                <button
                  onClick={() => {
                    setShowAddCategory(false);
                    setNewCategoryName('');
                    setCategoryErrors({});
                  }}
                  className="flex-1 px-4 py-2 border border-dark-600 rounded-lg text-dark-200 hover:bg-dark-700 transition-colors"
                >
                  {t('cancel')}
                </button>
                <button
                  onClick={handleAddCategory}
                  className="flex-1 px-4 py-2 bg-primary-600 hover:bg-primary-700 rounded-lg text-white transition-colors"
                >
                  {t('createCategory')}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Add New Subcategory Modal */}
      {showAddSubcategory && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-60">
          <div className="bg-dark-800 rounded-lg shadow-xl w-full max-w-sm mx-4">
            <div className="flex items-center justify-between p-4 border-b border-dark-700">
              <h3 className="text-lg font-semibold text-white">{t('addNewSubcategory')}</h3>
              <button
                onClick={() => {
                  setShowAddSubcategory(false);
                  setNewSubcategoryName('');
                  setNewSubcategoryParent('');
                  setCategoryErrors({});
                }}
                className="p-1 hover:bg-dark-700 rounded transition-colors"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
            <div className="p-4 space-y-4">
              <div>
                <label className="block text-sm font-medium text-dark-200 mb-2">
                  {t('parentCategory')} *
                </label>
                <select
                  value={newSubcategoryParent}
                  onChange={(e) => {
                    setNewSubcategoryParent(e.target.value);
                    if (categoryErrors.parent) {
                      setCategoryErrors(prev => ({ ...prev, parent: '' }));
                    }
                  }}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-lg text-white focus:outline-none focus:border-primary-500"
                >
                  <option value="">{t('selectParentCategory')}</option>
                  {allCategories.map(category => (
                    <option key={category.name} value={category.name}>
                      {tc(category.name)}
                    </option>
                  ))}
                </select>
                {categoryErrors.parent && (
                  <p className="text-red-400 text-xs mt-1">{categoryErrors.parent}</p>
                )}
              </div>
              <div>
                <label className="block text-sm font-medium text-dark-200 mb-2">
                  {t('subcategoryName')} *
                </label>
                <input
                  type="text"
                  value={newSubcategoryName}
                  onChange={(e) => {
                    setNewSubcategoryName(e.target.value);
                    if (categoryErrors.subcategory) {
                      setCategoryErrors(prev => ({ ...prev, subcategory: '' }));
                    }
                  }}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-lg text-white focus:outline-none focus:border-primary-500"
                  placeholder={t('enterSubcategoryName')}
                  autoFocus
                />
                {categoryErrors.subcategory && (
                  <p className="text-red-400 text-xs mt-1">{categoryErrors.subcategory}</p>
                )}
              </div>
              <div className="flex gap-3">
                <button
                  onClick={() => {
                    setShowAddSubcategory(false);
                    setNewSubcategoryName('');
                    setNewSubcategoryParent('');
                    setCategoryErrors({});
                  }}
                  className="flex-1 px-4 py-2 border border-dark-600 rounded-lg text-dark-200 hover:bg-dark-700 transition-colors"
                >
                  {t('cancel')}
                </button>
                <button
                  onClick={handleAddSubcategory}
                  className="flex-1 px-4 py-2 bg-primary-600 hover:bg-primary-700 rounded-lg text-white transition-colors"
                >
                  {t('createSubcategory')}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Duplicate Product Confirmation Dialog */}
      {showDuplicateDialog && duplicateProduct && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-60">
          <div className="bg-dark-800 rounded-lg shadow-xl w-full max-w-md mx-4">
            <div className="p-6">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-10 h-10 bg-yellow-600 rounded-full flex items-center justify-center">
                  <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-white">
                    {t('Duplicate Product Found') || 'Duplicate Product Found'}
                  </h3>
                  <p className="text-sm text-dark-300">
                    {t('A product with identical details already exists') || 'A product with identical details already exists'}
                  </p>
                </div>
              </div>

              <div className="bg-dark-700 rounded-lg p-4 mb-6">
                <p className="text-white text-sm mb-2">
                  {t('A product with the name') || 'A product with the name'} <span className="font-semibold text-primary-400">"{duplicateProduct.name}"</span> {t('already exists in category') || 'already exists in category'}:
                </p>
                <p className="text-dark-300 text-sm">
                  <span className="font-medium">{duplicateProduct.category}</span> → <span className="font-medium">{duplicateProduct.subcategory}</span>
                </p>
              </div>

              <p className="text-dark-300 text-sm mb-6">
                {t('Would you like to replace the existing product or modify this one?') || 'Would you like to replace the existing product or modify this one?'}
              </p>

              <div className="flex flex-col gap-3">
                <button
                  onClick={() => handleDuplicateResolution('replace')}
                  className="w-full px-4 py-3 bg-red-600 hover:bg-red-700 rounded-lg text-white font-medium transition-colors"
                >
                  {t('Replace Existing') || 'Replace Existing'}
                </button>

                <button
                  onClick={() => handleDuplicateResolution('modify')}
                  className="w-full px-4 py-3 bg-primary-600 hover:bg-primary-700 rounded-lg text-white font-medium transition-colors"
                >
                  {t('Modify This Product') || 'Modify This Product'}
                </button>

                <button
                  onClick={() => handleDuplicateResolution('cancel')}
                  className="w-full px-4 py-3 border border-dark-600 rounded-lg text-dark-200 hover:bg-dark-700 transition-colors"
                >
                  {t('Cancel') || 'Cancel'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default UploadModal;
