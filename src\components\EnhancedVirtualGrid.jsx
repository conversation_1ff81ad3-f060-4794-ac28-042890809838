import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { FixedSizeGrid as Grid } from 'react-window';
import { FixedSizeList as List } from 'react-window';
import InfiniteLoader from 'react-window-infinite-loader';

/**
 * Enhanced Virtual Grid for Large Datasets
 * Optimized for 100,000+ items with lazy loading and memory management
 */
const EnhancedVirtualGrid = ({
  items = [],
  itemHeight = 120,
  itemWidth = 200,
  containerHeight = 600,
  containerWidth = '100%',
  onItemClick,
  onItemsRendered,
  renderItem,
  loadMoreItems,
  hasNextPage = false,
  isNextPageLoading = false,
  threshold = 15,
  overscan = 5,
  enableInfiniteLoading = false,
  enableLazyLoading = true,
  chunkSize = 1000
}) => {
  const [visibleRange, setVisibleRange] = useState({ start: 0, end: 0 });
  const [loadedChunks, setLoadedChunks] = useState(new Set());
  const [itemCache, setItemCache] = useState(new Map());
  const gridRef = useRef();
  const observerRef = useRef();

  // Calculate grid dimensions
  const gridWidth = typeof containerWidth === 'string' ? 
    containerWidth : `${containerWidth}px`;
  
  const columnsCount = useMemo(() => {
    const availableWidth = typeof containerWidth === 'number' ? 
      containerWidth : 1200; // Default width
    return Math.floor(availableWidth / itemWidth);
  }, [containerWidth, itemWidth]);

  const rowsCount = Math.ceil(items.length / columnsCount);

  // Memoized item data for performance
  const itemData = useMemo(() => ({
    items,
    columnsCount,
    itemWidth,
    itemHeight,
    onItemClick,
    renderItem,
    itemCache,
    loadedChunks,
    enableLazyLoading
  }), [
    items, 
    columnsCount, 
    itemWidth, 
    itemHeight, 
    onItemClick, 
    renderItem,
    itemCache,
    loadedChunks,
    enableLazyLoading
  ]);

  // Lazy loading chunk management
  const loadChunk = useCallback(async (chunkIndex) => {
    if (loadedChunks.has(chunkIndex) || !enableLazyLoading) return;

    const startIndex = chunkIndex * chunkSize;
    const endIndex = Math.min(startIndex + chunkSize, items.length);
    
    // Simulate loading delay for demonstration
    await new Promise(resolve => setTimeout(resolve, 100));
    
    // Mark chunk as loaded
    setLoadedChunks(prev => new Set([...prev, chunkIndex]));
    
    // Cache items in this chunk
    setItemCache(prev => {
      const newCache = new Map(prev);
      for (let i = startIndex; i < endIndex; i++) {
        if (items[i]) {
          newCache.set(i, items[i]);
        }
      }
      return newCache;
    });
  }, [items, chunkSize, loadedChunks, enableLazyLoading]);

  // Handle visible range changes
  const handleItemsRendered = useCallback(({
    visibleRowStartIndex,
    visibleRowStopIndex,
    visibleColumnStartIndex,
    visibleColumnStopIndex
  }) => {
    const startIndex = visibleRowStartIndex * columnsCount + visibleColumnStartIndex;
    const endIndex = visibleRowStopIndex * columnsCount + visibleColumnStopIndex;
    
    setVisibleRange({ start: startIndex, end: endIndex });
    
    if (onItemsRendered) {
      onItemsRendered({
        visibleStartIndex: startIndex,
        visibleStopIndex: endIndex
      });
    }

    // Load chunks for visible items
    if (enableLazyLoading) {
      const startChunk = Math.floor(startIndex / chunkSize);
      const endChunk = Math.floor(endIndex / chunkSize);
      
      for (let chunk = startChunk; chunk <= endChunk; chunk++) {
        loadChunk(chunk);
      }
    }
  }, [columnsCount, onItemsRendered, enableLazyLoading, chunkSize, loadChunk]);

  // Infinite loading integration
  const isItemLoaded = useCallback((index) => {
    if (!enableInfiniteLoading) return true;
    return index < items.length;
  }, [enableInfiniteLoading, items.length]);

  const loadMoreItemsCallback = useCallback((startIndex, stopIndex) => {
    if (loadMoreItems && !isNextPageLoading) {
      return loadMoreItems(startIndex, stopIndex);
    }
    return Promise.resolve();
  }, [loadMoreItems, isNextPageLoading]);

  // Grid cell renderer
  const Cell = useCallback(({ columnIndex, rowIndex, style, data }) => {
    const itemIndex = rowIndex * data.columnsCount + columnIndex;
    const item = data.enableLazyLoading ? 
      data.itemCache.get(itemIndex) : 
      data.items[itemIndex];

    if (!item) {
      return (
        <div style={style} className="flex items-center justify-center bg-gray-100">
          <div className="animate-pulse bg-gray-300 rounded w-full h-full"></div>
        </div>
      );
    }

    return (
      <div style={style} className="p-1">
        {data.renderItem ? 
          data.renderItem(item, itemIndex) : 
          <DefaultItemRenderer item={item} index={itemIndex} onClick={data.onItemClick} />
        }
      </div>
    );
  }, []);

  // Performance monitoring
  useEffect(() => {
    const startTime = performance.now();
    
    return () => {
      const endTime = performance.now();
      console.log(`Virtual grid render time: ${endTime - startTime}ms`);
    };
  }, [items.length]);

  // Memory cleanup
  useEffect(() => {
    return () => {
      setItemCache(new Map());
      setLoadedChunks(new Set());
    };
  }, []);

  if (enableInfiniteLoading) {
    return (
      <div style={{ height: containerHeight, width: gridWidth }}>
        <InfiniteLoader
          isItemLoaded={isItemLoaded}
          itemCount={hasNextPage ? items.length + 1 : items.length}
          loadMoreItems={loadMoreItemsCallback}
          threshold={threshold}
        >
          {({ onItemsRendered: onInfiniteItemsRendered, ref }) => (
            <Grid
              ref={(grid) => {
                gridRef.current = grid;
                ref(grid);
              }}
              columnCount={columnsCount}
              columnWidth={itemWidth}
              height={containerHeight}
              rowCount={rowsCount}
              rowHeight={itemHeight}
              itemData={itemData}
              onItemsRendered={({
                visibleRowStartIndex,
                visibleRowStopIndex,
                visibleColumnStartIndex,
                visibleColumnStopIndex
              }) => {
                handleItemsRendered({
                  visibleRowStartIndex,
                  visibleRowStopIndex,
                  visibleColumnStartIndex,
                  visibleColumnStopIndex
                });
                
                onInfiniteItemsRendered({
                  visibleStartIndex: visibleRowStartIndex * columnsCount,
                  visibleStopIndex: visibleRowStopIndex * columnsCount
                });
              }}
              overscanRowCount={overscan}
              overscanColumnCount={overscan}
            >
              {Cell}
            </Grid>
          )}
        </InfiniteLoader>
      </div>
    );
  }

  return (
    <div style={{ height: containerHeight, width: gridWidth }}>
      <Grid
        ref={gridRef}
        columnCount={columnsCount}
        columnWidth={itemWidth}
        height={containerHeight}
        rowCount={rowsCount}
        rowHeight={itemHeight}
        itemData={itemData}
        onItemsRendered={handleItemsRendered}
        overscanRowCount={overscan}
        overscanColumnCount={overscan}
      >
        {Cell}
      </Grid>
    </div>
  );
};

/**
 * Default item renderer
 */
const DefaultItemRenderer = ({ item, index, onClick }) => (
  <div 
    className="bg-white border rounded-lg p-3 cursor-pointer hover:shadow-md transition-shadow"
    onClick={() => onClick && onClick(item, index)}
  >
    <div className="text-sm font-medium truncate">{item.name || `Item ${index}`}</div>
    <div className="text-xs text-gray-500 truncate">{item.category || 'No category'}</div>
    {item.price && (
      <div className="text-sm font-bold text-green-600">${item.price}</div>
    )}
  </div>
);

/**
 * Performance metrics component
 */
const VirtualGridMetrics = ({ items, visibleRange, loadedChunks, itemCache }) => {
  const [metrics, setMetrics] = useState({});

  useEffect(() => {
    const updateMetrics = () => {
      const memoryInfo = performance.memory ? {
        used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
        total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024),
        limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024)
      } : null;

      setMetrics({
        totalItems: items.length,
        visibleItems: visibleRange.end - visibleRange.start + 1,
        loadedChunks: loadedChunks.size,
        cachedItems: itemCache.size,
        memoryUsage: memoryInfo
      });
    };

    updateMetrics();
    const interval = setInterval(updateMetrics, 1000);
    
    return () => clearInterval(interval);
  }, [items.length, visibleRange, loadedChunks.size, itemCache.size]);

  return (
    <div className="text-xs text-gray-500 p-2 bg-gray-50 border-t">
      <div>Total: {metrics.totalItems?.toLocaleString()} | Visible: {metrics.visibleItems} | Cached: {metrics.cachedItems}</div>
      {metrics.memoryUsage && (
        <div>Memory: {metrics.memoryUsage.used}MB / {metrics.memoryUsage.total}MB</div>
      )}
    </div>
  );
};

export default EnhancedVirtualGrid;
export { VirtualGridMetrics };
