import React from 'react';

const ConnectionLine = ({ from, to }) => {
  // Calculate control points for a smooth curve
  const dx = to.x - from.x;
  const dy = to.y - from.y;
  
  // Create a curved path
  const midX = from.x + dx / 2;
  const midY = from.y + dy / 2;
  
  // Control points for the curve
  const cp1x = from.x + Math.abs(dx) * 0.3;
  const cp1y = from.y;
  const cp2x = to.x - Math.abs(dx) * 0.3;
  const cp2y = to.y;

  const pathData = `M ${from.x} ${from.y} C ${cp1x} ${cp1y}, ${cp2x} ${cp2y}, ${to.x} ${to.y}`;

  return (
    <g>
      {/* Connection line */}
      <path
        d={pathData}
        className="connection-line"
        strokeWidth="2"
        fill="none"
        stroke="#3b82f6"
        strokeDasharray="none"
      />
      
      {/* Connection points */}
      <circle
        cx={from.x}
        cy={from.y}
        r="4"
        fill="#3b82f6"
        stroke="#ffffff"
        strokeWidth="2"
      />
      <circle
        cx={to.x}
        cy={to.y}
        r="4"
        fill="#3b82f6"
        stroke="#ffffff"
        strokeWidth="2"
      />
      
      {/* Arrow at the end */}
      <defs>
        <marker
          id="arrowhead"
          markerWidth="10"
          markerHeight="7"
          refX="9"
          refY="3.5"
          orient="auto"
        >
          <polygon
            points="0 0, 10 3.5, 0 7"
            fill="#3b82f6"
          />
        </marker>
      </defs>
      
      <path
        d={pathData}
        className="connection-line"
        strokeWidth="2"
        fill="none"
        stroke="#3b82f6"
        markerEnd="url(#arrowhead)"
        opacity="0"
      />
    </g>
  );
};

export default ConnectionLine;
