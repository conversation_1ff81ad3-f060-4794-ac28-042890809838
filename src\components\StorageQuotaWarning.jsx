import React, { useState, useEffect } from 'react';
import { AlertTriangle, X, Trash2 } from 'lucide-react';
import { useSettings } from '../contexts/SettingsContext';

const StorageQuotaWarning = () => {
  const [showWarning, setShowWarning] = useState(false);
  const [storageInfo, setStorageInfo] = useState({ used: 0, total: 0 });
  const { clearAllLocalStorage, checkAndCleanupStorage } = useSettings();

  // Check storage usage periodically
  useEffect(() => {
    const checkStorage = () => {
      try {
        let totalSize = 0;
        for (let key in localStorage) {
          if (localStorage.hasOwnProperty(key)) {
            totalSize += localStorage[key].length;
          }
        }
        
        // Estimate total available (usually ~5-10MB)
        const estimatedTotal = 10 * 1024 * 1024; // 10MB
        const usagePercent = (totalSize / estimatedTotal) * 100;
        
        setStorageInfo({ used: totalSize, total: estimatedTotal, percent: usagePercent });
        
        // Show warning if usage is over 80%
        if (usagePercent > 80) {
          setShowWarning(true);
        }
      } catch (error) {
        console.error('Error checking storage:', error);
      }
    };

    checkStorage();
    const interval = setInterval(checkStorage, 30000); // Check every 30 seconds
    
    return () => clearInterval(interval);
  }, []);

  const handleCleanup = () => {
    checkAndCleanupStorage();
    setShowWarning(false);
  };

  const handleClearAll = () => {
    if (window.confirm('This will clear all saved data including custom products and projects. Are you sure?')) {
      clearAllLocalStorage();
      setShowWarning(false);
    }
  };

  const formatBytes = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  if (!showWarning) return null;

  return (
    <div className="fixed top-4 right-4 z-50 max-w-md">
      <div className="bg-yellow-900 border border-yellow-600 rounded-lg p-4 shadow-lg">
        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-3">
            <AlertTriangle className="w-5 h-5 text-yellow-400 mt-0.5" />
            <div className="flex-1">
              <h3 className="text-sm font-medium text-yellow-200">
                Storage Space Warning
              </h3>
              <p className="text-xs text-yellow-300 mt-1">
                Your browser storage is {storageInfo.percent?.toFixed(1)}% full 
                ({formatBytes(storageInfo.used)} used). 
                This may cause issues when saving new products.
              </p>
              <div className="mt-3 flex space-x-2">
                <button
                  onClick={handleCleanup}
                  className="text-xs bg-yellow-600 hover:bg-yellow-700 text-white px-3 py-1 rounded transition-colors"
                >
                  Clean Up
                </button>
                <button
                  onClick={handleClearAll}
                  className="text-xs bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded transition-colors flex items-center space-x-1"
                >
                  <Trash2 className="w-3 h-3" />
                  <span>Clear All</span>
                </button>
              </div>
            </div>
          </div>
          <button
            onClick={() => setShowWarning(false)}
            className="text-yellow-400 hover:text-yellow-200 transition-colors"
          >
            <X className="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
  );
};

export default StorageQuotaWarning;
