import React, { useState, useEffect } from 'react';
import { Activity, AlertTriangle, CheckCircle, Zap, Database, Clock } from 'lucide-react';
import { useData } from '../contexts/DataContext';

/**
 * Performance Monitor Component
 * Displays real-time performance metrics and memory usage for large datasets
 */
const PerformanceMonitor = ({ 
  isVisible = true, 
  position = 'bottom-right',
  compact = false 
}) => {
  const {
    isLargeDataset,
    datasetMetrics,
    processingProgress,
    memoryUsage,
    getPerformanceMetrics,
    forceMemoryCleanup
  } = useData();

  const [metrics, setMetrics] = useState({});
  const [isExpanded, setIsExpanded] = useState(!compact);
  const [alerts, setAlerts] = useState([]);

  // Update metrics periodically
  useEffect(() => {
    const updateMetrics = () => {
      const currentMetrics = getPerformanceMetrics();
      setMetrics(currentMetrics);
      
      // Check for performance alerts
      checkPerformanceAlerts(currentMetrics);
    };

    updateMetrics();
    const interval = setInterval(updateMetrics, 2000); // Update every 2 seconds

    return () => clearInterval(interval);
  }, [getPerformanceMetrics]);

  // Check for performance alerts
  const checkPerformanceAlerts = (currentMetrics) => {
    const newAlerts = [];

    // Memory usage alerts
    if (currentMetrics.memoryInfo?.ratio > 0.8) {
      newAlerts.push({
        type: 'warning',
        message: `High memory usage: ${Math.round(currentMetrics.memoryInfo.ratio * 100)}%`,
        action: 'cleanup'
      });
    }

    // Search performance alerts
    if (currentMetrics.averageSearchTime > 1000) {
      newAlerts.push({
        type: 'warning',
        message: `Slow search performance: ${Math.round(currentMetrics.averageSearchTime)}ms`,
        action: 'optimize'
      });
    }

    // Large dataset alerts
    if (currentMetrics.totalItems > 50000) {
      newAlerts.push({
        type: 'info',
        message: `Large dataset: ${currentMetrics.totalItems?.toLocaleString()} items`,
        action: 'info'
      });
    }

    setAlerts(newAlerts);
  };

  // Handle cleanup action
  const handleCleanup = () => {
    forceMemoryCleanup();
    setAlerts(alerts.filter(alert => alert.action !== 'cleanup'));
  };

  // Format memory size
  const formatMemorySize = (bytes) => {
    if (!bytes) return '0 MB';
    const mb = bytes / (1024 * 1024);
    return `${Math.round(mb)} MB`;
  };

  // Get status color based on performance
  const getStatusColor = () => {
    if (alerts.some(alert => alert.type === 'warning')) return 'text-yellow-600';
    if (alerts.some(alert => alert.type === 'error')) return 'text-red-600';
    return 'text-green-600';
  };

  // Position classes
  const positionClasses = {
    'top-left': 'top-4 left-4',
    'top-right': 'top-4 right-4',
    'bottom-left': 'bottom-4 left-4',
    'bottom-right': 'bottom-4 right-4'
  };

  if (!isVisible) return null;

  return (
    <div className={`fixed ${positionClasses[position]} z-50 bg-white border border-gray-200 rounded-lg shadow-lg max-w-sm`}>
      {/* Header */}
      <div 
        className="flex items-center justify-between p-3 cursor-pointer hover:bg-gray-50"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center space-x-2">
          <Activity className={`w-4 h-4 ${getStatusColor()}`} />
          <span className="text-sm font-medium text-gray-900">Performance</span>
          {isLargeDataset && (
            <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded">Large Dataset</span>
          )}
        </div>
        <div className="flex items-center space-x-1">
          {alerts.length > 0 && (
            <span className="w-2 h-2 bg-yellow-500 rounded-full"></span>
          )}
          <span className="text-xs text-gray-500">
            {isExpanded ? '−' : '+'}
          </span>
        </div>
      </div>

      {/* Expanded Content */}
      {isExpanded && (
        <div className="border-t border-gray-200">
          {/* Processing Progress */}
          {processingProgress > 0 && processingProgress < 100 && (
            <div className="p-3 border-b border-gray-100">
              <div className="flex items-center justify-between mb-1">
                <span className="text-xs text-gray-600">Processing</span>
                <span className="text-xs text-gray-900">{Math.round(processingProgress)}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${processingProgress}%` }}
                ></div>
              </div>
            </div>
          )}

          {/* Memory Usage */}
          {memoryUsage.used && (
            <div className="p-3 border-b border-gray-100">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center space-x-1">
                  <Database className="w-3 h-3 text-gray-500" />
                  <span className="text-xs text-gray-600">Memory</span>
                </div>
                <span className="text-xs text-gray-900">
                  {formatMemorySize(memoryUsage.used)} / {formatMemorySize(memoryUsage.limit)}
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className={`h-2 rounded-full transition-all duration-300 ${
                    memoryUsage.ratio > 0.8 ? 'bg-red-500' : 
                    memoryUsage.ratio > 0.6 ? 'bg-yellow-500' : 'bg-green-500'
                  }`}
                  style={{ width: `${(memoryUsage.ratio || 0) * 100}%` }}
                ></div>
              </div>
            </div>
          )}

          {/* Dataset Metrics */}
          {metrics.totalItems > 0 && (
            <div className="p-3 border-b border-gray-100">
              <div className="grid grid-cols-2 gap-2 text-xs">
                <div>
                  <span className="text-gray-600">Items:</span>
                  <span className="text-gray-900 ml-1 font-medium">
                    {metrics.totalItems?.toLocaleString()}
                  </span>
                </div>
                <div>
                  <span className="text-gray-600">Indexed:</span>
                  <span className="text-gray-900 ml-1 font-medium">
                    {metrics.indexedItems?.toLocaleString()}
                  </span>
                </div>
                {metrics.averageSearchTime > 0 && (
                  <>
                    <div>
                      <span className="text-gray-600">Search:</span>
                      <span className="text-gray-900 ml-1 font-medium">
                        {Math.round(metrics.averageSearchTime)}ms
                      </span>
                    </div>
                    <div>
                      <span className="text-gray-600">Searches:</span>
                      <span className="text-gray-900 ml-1 font-medium">
                        {metrics.totalSearches}
                      </span>
                    </div>
                  </>
                )}
              </div>
            </div>
          )}

          {/* Alerts */}
          {alerts.length > 0 && (
            <div className="p-3 border-b border-gray-100">
              <div className="space-y-2">
                {alerts.map((alert, index) => (
                  <div key={index} className="flex items-start space-x-2">
                    {alert.type === 'warning' && <AlertTriangle className="w-3 h-3 text-yellow-500 mt-0.5" />}
                    {alert.type === 'error' && <AlertTriangle className="w-3 h-3 text-red-500 mt-0.5" />}
                    {alert.type === 'info' && <CheckCircle className="w-3 h-3 text-blue-500 mt-0.5" />}
                    <div className="flex-1">
                      <p className="text-xs text-gray-700">{alert.message}</p>
                      {alert.action === 'cleanup' && (
                        <button
                          onClick={handleCleanup}
                          className="text-xs text-blue-600 hover:text-blue-800 mt-1"
                        >
                          Clean up memory
                        </button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Actions */}
          <div className="p-3">
            <div className="flex space-x-2">
              <button
                onClick={handleCleanup}
                className="flex-1 flex items-center justify-center space-x-1 px-2 py-1 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded text-xs transition-colors"
              >
                <Zap className="w-3 h-3" />
                <span>Cleanup</span>
              </button>
              <button
                onClick={() => setMetrics(getPerformanceMetrics())}
                className="flex-1 flex items-center justify-center space-x-1 px-2 py-1 bg-blue-100 hover:bg-blue-200 text-blue-700 rounded text-xs transition-colors"
              >
                <Clock className="w-3 h-3" />
                <span>Refresh</span>
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

/**
 * Compact Performance Indicator
 * Shows minimal performance info in the UI
 */
const PerformanceIndicator = ({ className = '' }) => {
  const { memoryUsage, isLargeDataset, processingProgress } = useData();
  
  if (!isLargeDataset && processingProgress === 0) return null;

  const getIndicatorColor = () => {
    if (processingProgress > 0) return 'bg-blue-500';
    if (memoryUsage.ratio > 0.8) return 'bg-red-500';
    if (memoryUsage.ratio > 0.6) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      <div className={`w-2 h-2 rounded-full ${getIndicatorColor()}`}></div>
      {processingProgress > 0 && (
        <span className="text-xs text-gray-600">
          Processing {Math.round(processingProgress)}%
        </span>
      )}
      {isLargeDataset && processingProgress === 0 && (
        <span className="text-xs text-gray-600">Large Dataset</span>
      )}
    </div>
  );
};

export default PerformanceMonitor;
export { PerformanceIndicator };
