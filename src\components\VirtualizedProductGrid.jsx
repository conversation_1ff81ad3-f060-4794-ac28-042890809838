import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { ChevronDown, ChevronRight } from 'lucide-react';
import ProductItem from './ProductItem';
import HighlightedText from './HighlightedText';
import { useLanguage } from '../contexts/LanguageContext';
import {
  flattenProductHierarchy,
  findVisibleItems,
  calculateFlatListHeight,
  getItemHeight,
  throttle
} from '../utils/virtualScrollUtils';

const VirtualizedProductGrid = ({
  groupedProducts,
  expandedCategories,
  shouldAutoExpand,
  searchTerm,
  onToggleCategory,
  onToggleSubcategory,
  onProductDragStart
}) => {
  const { t, tc } = useLanguage();
  const containerRef = useRef(null);
  const [scrollTop, setScrollTop] = useState(0);
  const [containerHeight, setContainerHeight] = useState(0);

  // Flatten the hierarchical structure for virtual scrolling
  const flatItems = useMemo(() => {
    return flattenProductHierarchy(groupedProducts, expandedCategories, shouldAutoExpand);
  }, [groupedProducts, expandedCategories, shouldAutoExpand]);

  // Calculate total height
  const totalHeight = useMemo(() => {
    return calculateFlatListHeight(flatItems);
  }, [flatItems]);

  // Find visible items
  const { visibleItems, offsetY } = useMemo(() => {
    // If container height is not set yet, show all items initially
    if (containerHeight === 0) {
      return {
        visibleItems: flatItems,
        offsetY: 0
      };
    }

    return findVisibleItems(flatItems, scrollTop, containerHeight, 3);
  }, [flatItems, scrollTop, containerHeight]);

  // Update container height when component mounts or resizes
  useEffect(() => {
    const updateHeight = () => {
      if (containerRef.current) {
        setContainerHeight(containerRef.current.clientHeight);
      }
    };

    updateHeight();
    window.addEventListener('resize', updateHeight);
    return () => window.removeEventListener('resize', updateHeight);
  }, []);

  // Throttled scroll handler
  const handleScroll = useCallback(
    throttle((e) => {
      setScrollTop(e.target.scrollTop);
    }, 16), // ~60fps
    []
  );

  // Update container height on resize and mount
  useEffect(() => {
    const updateHeight = () => {
      if (containerRef.current) {
        setContainerHeight(containerRef.current.clientHeight);
      }
    };

    // Initial measurement
    updateHeight();

    // Listen for resize events
    window.addEventListener('resize', updateHeight);

    return () => window.removeEventListener('resize', updateHeight);
  }, []);

  // Render individual item based on type
  const renderItem = useCallback((item, index) => {
    const key = `${item.type}-${item.id}-${index}`;
    
    switch (item.type) {
      case 'category':
        return (
          <div key={key} className="mb-4" style={{ height: getItemHeight('category') }}>
            <button
              onClick={() => onToggleCategory(item.category)}
              className="w-full flex items-center justify-between p-2 rounded-lg hover:bg-dark-700 transition-colors"
            >
              <HighlightedText
                text={tc(item.category)}
                searchTerm={searchTerm}
                className="font-medium text-white"
                highlightClassName="bg-yellow-400 text-yellow-900 px-1 rounded"
              />
              {item.isExpanded ? (
                <ChevronDown className="w-4 h-4 text-dark-400" />
              ) : (
                <ChevronRight className="w-4 h-4 text-dark-400" />
              )}
            </button>
          </div>
        );

      case 'subcategory':
        return (
          <div key={key} className="ml-4 mb-2" style={{ height: getItemHeight('subcategory') }}>
            <button
              onClick={() => onToggleSubcategory(item.category, item.subcategory)}
              className="w-full flex items-center justify-between p-2 rounded-lg hover:bg-dark-700 transition-colors"
            >
              <HighlightedText
                text={item.subcategory}
                searchTerm={searchTerm}
                className="text-sm text-dark-200"
                highlightClassName="bg-yellow-400 text-yellow-900 px-1 rounded"
              />
              <div className="flex items-center gap-2">
                <span className="text-xs text-dark-400 bg-dark-600 px-2 py-1 rounded">
                  {item.productCount}
                </span>
                {item.isExpanded ? (
                  <ChevronDown className="w-3 h-3 text-dark-400" />
                ) : (
                  <ChevronRight className="w-3 h-3 text-dark-400" />
                )}
              </div>
            </button>
          </div>
        );

      case 'product-row':
        return (
          <div 
            key={key} 
            className="ml-8 mb-4 grid grid-cols-2 gap-4" 
            style={{ height: getItemHeight('product-row') }}
          >
            {item.products.map(product => (
              <ProductItem
                key={product.id}
                product={product}
                onDragStart={onProductDragStart}
                searchTerm={searchTerm}
              />
            ))}
          </div>
        );

      default:
        return null;
    }
  }, [searchTerm, onToggleCategory, onToggleSubcategory, onProductDragStart, tc, t]);

  // Handle empty state
  if (Object.keys(groupedProducts).length === 0) {
    return (
      <div className="text-center text-dark-400 py-8">
        <div className="w-8 h-8 mx-auto mb-2 opacity-50">
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
          </svg>
        </div>
        <p>{t('noProductsFound')}</p>
      </div>
    );
  }

  // Fallback to non-virtual rendering if there are issues
  if (flatItems.length === 0 || (visibleItems.length === 0 && containerHeight > 0)) {
    return (
      <div className="flex-1 overflow-y-auto sidebar-scroll p-4" style={{ WebkitOverflowScrolling: 'touch' }}>
        <div className="text-center text-yellow-400 py-4 mb-4 bg-yellow-900 bg-opacity-20 rounded">
          <p className="text-sm">Virtual scrolling disabled - using fallback rendering</p>
        </div>
        {flatItems.map((item, index) => renderItem(item, index))}
      </div>
    );
  }

  return (
    <div
      ref={containerRef}
      className="h-full overflow-y-auto sidebar-scroll"
      onScroll={handleScroll}
      style={{
        WebkitOverflowScrolling: 'touch',
        willChange: 'scroll-position',
        overscrollBehavior: 'contain',
        maxHeight: '100%'
      }}
    >
      {/* Virtual scroll container */}
      <div style={{ height: totalHeight, position: 'relative', minHeight: '100%' }}>
        {/* Visible items container */}
        <div
          style={{
            transform: `translateY(${offsetY}px)`,
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            willChange: 'transform'
          }}
        >
          <div className="p-4">
            {visibleItems.map((item, index) => renderItem(item, index))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default VirtualizedProductGrid;
