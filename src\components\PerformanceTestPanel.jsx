import React, { useState } from 'react';
import { Play, Trash2, BarChart3, Clock, Database } from 'lucide-react';
import { generateTestProducts, clearTestData, performanceTest } from '../utils/testDataGenerator';
import { useLanguage } from '../contexts/LanguageContext';

const PerformanceTestPanel = ({ 
  onAddTestProducts, 
  onClearTestData, 
  currentProductCount,
  testProductCount 
}) => {
  const { t } = useLanguage();
  const [isGenerating, setIsGenerating] = useState(false);
  const [testResults, setTestResults] = useState([]);
  const [selectedCount, setSelectedCount] = useState(1000);

  const testSizes = [
    { label: '1K Products', value: 1000 },
    { label: '5K Products', value: 5000 },
    { label: '10K Products', value: 10000 },
    { label: '25K Products', value: 25000 },
    { label: '50K Products', value: 50000 }
  ];

  const handleGenerateTestData = async (count) => {
    setIsGenerating(true);
    
    try {
      const result = performanceTest(() => {
        const products = generateTestProducts(count);
        onAddTestProducts(products);
        return products;
      }, `Generate ${count} products`);
      
      setTestResults(prev => [...prev, {
        id: Date.now(),
        type: 'generation',
        count,
        duration: result.duration,
        memoryUsed: result.memoryUsed,
        timestamp: new Date().toLocaleTimeString()
      }]);
      
    } catch (error) {
      console.error('Error generating test data:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  const handleClearTestData = () => {
    const result = performanceTest(() => {
      onClearTestData();
    }, 'Clear test data');
    
    setTestResults(prev => [...prev, {
      id: Date.now(),
      type: 'clear',
      duration: result.duration,
      memoryUsed: result.memoryUsed,
      timestamp: new Date().toLocaleTimeString()
    }]);
  };

  const handleScrollTest = () => {
    const result = performanceTest(() => {
      // Simulate rapid scrolling
      const sidebar = document.querySelector('.sidebar-container .flex-1');
      if (sidebar) {
        const scrollHeight = sidebar.scrollHeight;
        const steps = 20;
        const stepSize = scrollHeight / steps;
        
        for (let i = 0; i < steps; i++) {
          setTimeout(() => {
            sidebar.scrollTop = i * stepSize;
          }, i * 50);
        }
      }
    }, 'Scroll performance test');
    
    setTestResults(prev => [...prev, {
      id: Date.now(),
      type: 'scroll',
      duration: result.duration,
      memoryUsed: result.memoryUsed,
      timestamp: new Date().toLocaleTimeString()
    }]);
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <div className="bg-dark-700 border border-dark-600 rounded-lg p-4 mb-4">
      <div className="flex items-center gap-2 mb-4">
        <BarChart3 className="w-5 h-5 text-primary-400" />
        <h3 className="text-lg font-semibold text-white">Virtual Scrolling Performance Test</h3>
      </div>
      
      {/* Current Stats */}
      <div className="grid grid-cols-2 gap-4 mb-4">
        <div className="bg-dark-800 rounded-lg p-3">
          <div className="text-sm text-dark-300">Total Products</div>
          <div className="text-xl font-bold text-white">{currentProductCount.toLocaleString()}</div>
        </div>
        <div className="bg-dark-800 rounded-lg p-3">
          <div className="text-sm text-dark-300">Test Products</div>
          <div className="text-xl font-bold text-primary-400">{testProductCount.toLocaleString()}</div>
        </div>
      </div>

      {/* Test Controls */}
      <div className="space-y-3 mb-4">
        <div>
          <label className="block text-sm font-medium text-dark-200 mb-2">
            Generate Test Products
          </label>
          <div className="flex flex-wrap gap-2">
            {testSizes.map(size => (
              <button
                key={size.value}
                onClick={() => handleGenerateTestData(size.value)}
                disabled={isGenerating}
                className="px-3 py-2 bg-primary-600 hover:bg-primary-700 disabled:bg-dark-600 disabled:cursor-not-allowed text-white rounded-lg text-sm transition-colors flex items-center gap-2"
              >
                <Play className="w-3 h-3" />
                {size.label}
              </button>
            ))}
          </div>
        </div>

        <div className="flex gap-2">
          <button
            onClick={handleScrollTest}
            disabled={currentProductCount === 0}
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-dark-600 disabled:cursor-not-allowed text-white rounded-lg text-sm transition-colors flex items-center gap-2"
          >
            <Clock className="w-4 h-4" />
            Test Scroll Performance
          </button>
          
          <button
            onClick={handleClearTestData}
            disabled={testProductCount === 0}
            className="px-4 py-2 bg-red-600 hover:bg-red-700 disabled:bg-dark-600 disabled:cursor-not-allowed text-white rounded-lg text-sm transition-colors flex items-center gap-2"
          >
            <Trash2 className="w-4 h-4" />
            Clear Test Data
          </button>
        </div>
      </div>

      {/* Test Results */}
      {testResults.length > 0 && (
        <div>
          <div className="flex items-center justify-between mb-2">
            <h4 className="text-sm font-medium text-dark-200">Performance Results</h4>
            <button
              onClick={clearResults}
              className="text-xs text-dark-400 hover:text-white transition-colors"
            >
              Clear Results
            </button>
          </div>
          
          <div className="bg-dark-800 rounded-lg p-3 max-h-40 overflow-y-auto">
            {testResults.slice(-10).reverse().map(result => (
              <div key={result.id} className="flex justify-between items-center py-1 text-xs">
                <div className="flex items-center gap-2">
                  <span className={`w-2 h-2 rounded-full ${
                    result.type === 'generation' ? 'bg-green-400' :
                    result.type === 'clear' ? 'bg-red-400' : 'bg-blue-400'
                  }`} />
                  <span className="text-dark-300">
                    {result.type === 'generation' && `Generated ${result.count?.toLocaleString()} products`}
                    {result.type === 'clear' && 'Cleared test data'}
                    {result.type === 'scroll' && 'Scroll test'}
                  </span>
                </div>
                <div className="flex gap-3 text-dark-400">
                  <span>{result.duration.toFixed(1)}ms</span>
                  {result.memoryUsed && (
                    <span>{(result.memoryUsed / 1024 / 1024).toFixed(1)}MB</span>
                  )}
                  <span>{result.timestamp}</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Performance Tips */}
      <div className="mt-4 p-3 bg-dark-800 rounded-lg">
        <h5 className="text-sm font-medium text-dark-200 mb-2">Virtual Scrolling Benefits:</h5>
        <ul className="text-xs text-dark-400 space-y-1">
          <li>• Only renders visible items (~10-20 items vs thousands)</li>
          <li>• Constant memory usage regardless of total product count</li>
          <li>• Smooth scrolling performance with 10K+ products</li>
          <li>• Maintains search and filtering functionality</li>
          <li>• Preserves drag-and-drop and hover interactions</li>
        </ul>
      </div>
    </div>
  );
};

export default PerformanceTestPanel;
