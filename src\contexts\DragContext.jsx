import React, { createContext, useContext, useState, useRef, useEffect } from 'react';

const DragContext = createContext();

export const useDrag = () => {
  const context = useContext(DragContext);
  if (!context) {
    throw new Error('useDrag must be used within a DragProvider');
  }
  return context;
};

export const DragProvider = ({ children }) => {
  const [isDragging, setIsDragging] = useState(false);
  const [dragSource, setDragSource] = useState(null); // 'sidebar' or 'canvas'
  const [draggedProduct, setDraggedProduct] = useState(null);
  const dragTimeoutRef = useRef(null);
  const dragStartTimeRef = useRef(null);

  // Start drag operation
  const startDrag = (source, product = null) => {
    // Clear any existing timeout
    if (dragTimeoutRef.current) {
      clearTimeout(dragTimeoutRef.current);
    }

    dragStartTimeRef.current = Date.now();
    setDragSource(source);
    setDraggedProduct(product);
    
    // Set dragging state with a small delay to avoid false positives
    dragTimeoutRef.current = setTimeout(() => {
      setIsDragging(true);
    }, 50); // 50ms delay to ensure it's a real drag operation
  };

  // End drag operation
  const endDrag = () => {
    // Clear timeout if drag ends before delay completes
    if (dragTimeoutRef.current) {
      clearTimeout(dragTimeoutRef.current);
      dragTimeoutRef.current = null;
    }

    setIsDragging(false);
    setDragSource(null);
    setDraggedProduct(null);
    dragStartTimeRef.current = null;
  };

  // Check if drag operation is from sidebar
  const isDraggingFromSidebar = () => {
    return isDragging && dragSource === 'sidebar';
  };

  // Check if drag operation is from canvas
  const isDraggingFromCanvas = () => {
    return isDragging && dragSource === 'canvas';
  };

  // Get drag duration
  const getDragDuration = () => {
    if (!dragStartTimeRef.current) return 0;
    return Date.now() - dragStartTimeRef.current;
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (dragTimeoutRef.current) {
        clearTimeout(dragTimeoutRef.current);
      }
    };
  }, []);

  // Global mouse and touch event listeners to detect drag end
  useEffect(() => {
    const handleMouseUp = () => {
      if (isDragging) {
        endDrag();
      }
    };

    const handleTouchEnd = () => {
      if (isDragging) {
        endDrag();
      }
    };

    const handleDragEnd = () => {
      if (isDragging) {
        endDrag();
      }
    };

    // Add global listeners for mouse, touch, and drag events
    document.addEventListener('mouseup', handleMouseUp);
    document.addEventListener('touchend', handleTouchEnd);
    document.addEventListener('touchcancel', handleTouchEnd);
    document.addEventListener('dragend', handleDragEnd);

    return () => {
      document.removeEventListener('mouseup', handleMouseUp);
      document.removeEventListener('touchend', handleTouchEnd);
      document.removeEventListener('touchcancel', handleTouchEnd);
      document.removeEventListener('dragend', handleDragEnd);
    };
  }, [isDragging]);

  const value = {
    isDragging,
    dragSource,
    draggedProduct,
    startDrag,
    endDrag,
    isDraggingFromSidebar,
    isDraggingFromCanvas,
    getDragDuration
  };

  return (
    <DragContext.Provider value={value}>
      {children}
    </DragContext.Provider>
  );
};

export default DragContext;
