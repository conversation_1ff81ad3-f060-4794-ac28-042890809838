import React, { useState, useEffect, useRef } from 'react';
import { renderProductAsPNG, getCachedProductPNG } from '../utils/productImageUtils';

/**
 * Component that renders a product as a PNG image with transparent background
 * Provides clean, isolated product display without borders or UI elements
 */
const ProductPNGRenderer = ({
  product,
  width = 100,
  height = 70,
  className = "",
  onImageReady = null,
  onImageError = null,
  ...props
}) => {
  const [pngDataUrl, setPngDataUrl] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const mountedRef = useRef(true);

  useEffect(() => {
    mountedRef.current = true;
    return () => {
      mountedRef.current = false;
    };
  }, []);

  useEffect(() => {
    const loadProductPNG = async () => {
      if (!product) {
        setIsLoading(false);
        setHasError(true);
        return;
      }

      try {
        setIsLoading(true);
        setHasError(false);

        // Check if we have a cached version first
        const cached = getCachedProductPNG(product.id, {
          width,
          height,
          scale: 2,
          backgroundColor: 'transparent'
        });

        if (cached) {
          if (mountedRef.current) {
            setPngDataUrl(cached);
            setIsLoading(false);
            onImageReady?.(cached);
          }
          return;
        }

        // Render new PNG
        const pngUrl = await renderProductAsPNG(product, {
          width,
          height,
          scale: 2,
          backgroundColor: 'transparent',
          quality: 1.0
        });

        if (mountedRef.current) {
          setPngDataUrl(pngUrl);
          setIsLoading(false);
          onImageReady?.(pngUrl);
        }

      } catch (error) {
        console.error('Error loading product PNG:', error);
        if (mountedRef.current) {
          setHasError(true);
          setIsLoading(false);
          onImageError?.(error);
        }
      }
    };

    loadProductPNG();
  }, [product, width, height, onImageReady, onImageError]);

  // Loading state - show a subtle placeholder
  if (isLoading) {
    return (
      <div
        style={{
          width: `${width}px`,
          height: `${height}px`,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          background: 'transparent'
        }}
        className={className}
        {...props}
      >
        <div
          className="animate-pulse rounded border-2 border-dashed border-gray-400 opacity-50"
          style={{
            width: '80%',
            height: '80%',
            background: 'transparent'
          }}
        />
      </div>
    );
  }

  // Error state - show fallback
  if (hasError || !pngDataUrl) {
    return (
      <div
        style={{
          width: `${width}px`,
          height: `${height}px`,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          background: 'transparent',
          color: 'white',
          fontSize: '12px',
          fontWeight: 'bold',
          textShadow: '1px 1px 2px rgba(0,0,0,0.8)'
        }}
        className={className}
        {...props}
      >
        {product?.name?.substring(0, 3).toUpperCase() || 'ERR'}
      </div>
    );
  }

  // Render the PNG image with proper aspect ratio preservation and rotation
  const rotation = product.rotation || 0;

  return (
    <div
      style={{
        width: `${width}px`,
        height: `${height}px`,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        background: 'transparent'
      }}
      className={className}
    >
      <img
        src={pngDataUrl}
        alt={product.name}
        style={{
          maxWidth: '100%',
          maxHeight: '100%',
          width: 'auto',
          height: 'auto',
          objectFit: 'contain', // Maintain aspect ratio without stretching
          objectPosition: 'center', // Center the image within container
          imageRendering: 'crisp-edges', // Ensure sharp rendering
          background: 'transparent', // Ensure transparent background
          display: 'block', // Prevent inline spacing issues
          transform: `rotate(${rotation}deg)`, // Apply rotation
          transformOrigin: 'center', // Rotate around center
          transition: 'transform 0.2s ease-in-out' // Smooth rotation animation
        }}
        {...props}
      />
    </div>
  );
};

export default ProductPNGRenderer;
