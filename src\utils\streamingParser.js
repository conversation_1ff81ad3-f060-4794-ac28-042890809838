/**
 * Streaming JSON Parser for Large Files
 * Handles multi-gigabyte JSON files by processing them in chunks
 */

class StreamingJSONParser {
  constructor(options = {}) {
    this.chunkSize = options.chunkSize || 10 * 1024 * 1024; // 10MB chunks
    this.onProgress = options.onProgress || (() => {});
    this.onChunk = options.onChunk || (() => {});
    this.onComplete = options.onComplete || (() => {});
    this.onError = options.onError || (() => {});
    
    this.buffer = '';
    this.totalSize = 0;
    this.processedSize = 0;
    this.isProcessing = false;
    this.aborted = false;
  }

  /**
   * Parse a large JSON file using streaming
   */
  async parseFile(file) {
    if (this.isProcessing) {
      throw new Error('Parser is already processing a file');
    }

    this.isProcessing = true;
    this.aborted = false;
    this.totalSize = file.size;
    this.processedSize = 0;
    this.buffer = '';

    try {
      // For very large files, use streaming approach
      if (file.size > 100 * 1024 * 1024) { // 100MB+
        await this.streamParseFile(file);
      } else {
        // For smaller files, use traditional approach
        await this.traditionalParseFile(file);
      }
    } catch (error) {
      this.onError(error);
      throw error;
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * Stream parse large files in chunks
   */
  async streamParseFile(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      let offset = 0;
      let jsonDepth = 0;
      let inString = false;
      let escapeNext = false;
      let currentObject = '';
      let objectStart = -1;

      const readChunk = () => {
        if (this.aborted) {
          reject(new Error('Parsing aborted'));
          return;
        }

        if (offset >= file.size) {
          this.finalizeProcessing(resolve);
          return;
        }

        const chunk = file.slice(offset, offset + this.chunkSize);
        reader.readAsText(chunk);
      };

      reader.onload = (e) => {
        const chunkText = e.target.result;
        this.processedSize += chunkText.length;
        
        // Process chunk character by character to find complete JSON objects
        for (let i = 0; i < chunkText.length; i++) {
          const char = chunkText[i];
          
          if (escapeNext) {
            escapeNext = false;
            continue;
          }

          if (char === '\\' && inString) {
            escapeNext = true;
            continue;
          }

          if (char === '"' && !escapeNext) {
            inString = !inString;
            continue;
          }

          if (!inString) {
            if (char === '{') {
              if (jsonDepth === 0) {
                objectStart = this.buffer.length + i;
              }
              jsonDepth++;
            } else if (char === '}') {
              jsonDepth--;
              if (jsonDepth === 0 && objectStart !== -1) {
                // Complete object found
                const objectJson = this.buffer.substring(objectStart) + chunkText.substring(0, i + 1);
                this.processCompleteObject(objectJson);
                objectStart = -1;
              }
            }
          }
        }

        this.buffer += chunkText;
        
        // Report progress
        const progress = (this.processedSize / this.totalSize) * 100;
        this.onProgress(progress, this.processedSize, this.totalSize);

        offset += this.chunkSize;
        
        // Continue reading next chunk
        setTimeout(readChunk, 10); // Small delay to keep UI responsive
      };

      reader.onerror = () => {
        reject(new Error('Error reading file chunk'));
      };

      readChunk();
    });
  }

  /**
   * Traditional parsing for smaller files
   */
  async traditionalParseFile(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      
      reader.onload = (e) => {
        try {
          const data = JSON.parse(e.target.result);
          this.onComplete(data);
          resolve(data);
        } catch (error) {
          reject(new Error('Invalid JSON format: ' + error.message));
        }
      };

      reader.onerror = () => {
        reject(new Error('Error reading file'));
      };

      reader.onprogress = (e) => {
        if (e.lengthComputable) {
          const progress = (e.loaded / e.total) * 100;
          this.onProgress(progress, e.loaded, e.total);
        }
      };

      reader.readAsText(file);
    });
  }

  /**
   * Process a complete JSON object
   */
  processCompleteObject(objectJson) {
    try {
      const obj = JSON.parse(objectJson);
      this.onChunk(obj);
    } catch (error) {
      console.warn('Failed to parse object chunk:', error);
    }
  }

  /**
   * Finalize processing
   */
  finalizeProcessing(resolve) {
    // Process any remaining buffer
    if (this.buffer.trim()) {
      try {
        const finalData = JSON.parse(this.buffer);
        this.onComplete(finalData);
        resolve(finalData);
      } catch (error) {
        // Try to extract what we can
        this.onComplete({ partial: true, error: error.message });
        resolve({ partial: true, error: error.message });
      }
    } else {
      this.onComplete({ success: true });
      resolve({ success: true });
    }
  }

  /**
   * Abort parsing
   */
  abort() {
    this.aborted = true;
    this.isProcessing = false;
  }

  /**
   * Check if parser is currently processing
   */
  isActive() {
    return this.isProcessing;
  }
}

/**
 * Chunked Data Processor
 * Processes large datasets in manageable chunks
 */
class ChunkedDataProcessor {
  constructor(options = {}) {
    this.chunkSize = options.chunkSize || 1000; // Process 1000 items at a time
    this.onProgress = options.onProgress || (() => {});
    this.onChunk = options.onChunk || (() => {});
    this.onComplete = options.onComplete || (() => {});
    this.delay = options.delay || 10; // Delay between chunks
  }

  /**
   * Process large array in chunks
   */
  async processArray(array, processor) {
    const totalItems = array.length;
    let processedItems = 0;

    for (let i = 0; i < array.length; i += this.chunkSize) {
      const chunk = array.slice(i, i + this.chunkSize);
      
      // Process chunk
      const processedChunk = await this.processChunk(chunk, processor);
      this.onChunk(processedChunk, i);

      processedItems += chunk.length;
      const progress = (processedItems / totalItems) * 100;
      this.onProgress(progress, processedItems, totalItems);

      // Yield control to prevent blocking
      if (this.delay > 0) {
        await new Promise(resolve => setTimeout(resolve, this.delay));
      }
    }

    this.onComplete();
  }

  /**
   * Process a single chunk
   */
  async processChunk(chunk, processor) {
    if (typeof processor === 'function') {
      return processor(chunk);
    }
    return chunk;
  }
}

/**
 * Memory Monitor
 * Monitors memory usage and provides warnings
 */
class MemoryMonitor {
  constructor(options = {}) {
    this.warningThreshold = options.warningThreshold || 0.8; // 80% of available memory
    this.criticalThreshold = options.criticalThreshold || 0.9; // 90% of available memory
    this.onWarning = options.onWarning || (() => {});
    this.onCritical = options.onCritical || (() => {});
    this.checkInterval = options.checkInterval || 5000; // Check every 5 seconds
    
    this.isMonitoring = false;
    this.intervalId = null;
  }

  /**
   * Start monitoring memory usage
   */
  startMonitoring() {
    if (this.isMonitoring) return;

    this.isMonitoring = true;
    this.intervalId = setInterval(() => {
      this.checkMemoryUsage();
    }, this.checkInterval);
  }

  /**
   * Stop monitoring
   */
  stopMonitoring() {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
    this.isMonitoring = false;
  }

  /**
   * Check current memory usage
   */
  checkMemoryUsage() {
    if ('memory' in performance) {
      const memory = performance.memory;
      const usageRatio = memory.usedJSHeapSize / memory.jsHeapSizeLimit;

      if (usageRatio >= this.criticalThreshold) {
        this.onCritical(memory, usageRatio);
      } else if (usageRatio >= this.warningThreshold) {
        this.onWarning(memory, usageRatio);
      }

      return {
        used: memory.usedJSHeapSize,
        total: memory.totalJSHeapSize,
        limit: memory.jsHeapSizeLimit,
        ratio: usageRatio
      };
    }

    return null;
  }

  /**
   * Get current memory info
   */
  getMemoryInfo() {
    return this.checkMemoryUsage();
  }

  /**
   * Force garbage collection (if available)
   */
  forceGC() {
    if ('gc' in window && typeof window.gc === 'function') {
      window.gc();
      return true;
    }
    return false;
  }
}

export { StreamingJSONParser, ChunkedDataProcessor, MemoryMonitor };
