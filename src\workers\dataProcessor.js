/**
 * Web Worker for Background Data Processing
 * Handles heavy data processing tasks without blocking the main thread
 */

// Import utilities (Note: In a real implementation, you'd need to handle imports differently in workers)
class WorkerDataProcessor {
  constructor() {
    this.isProcessing = false;
    this.currentTask = null;
  }

  /**
   * Process large dataset in background
   */
  async processLargeDataset(data, options = {}) {
    this.isProcessing = true;
    this.currentTask = 'processing_dataset';

    try {
      const result = await this.chunkProcessData(data, options);
      this.postMessage({
        type: 'PROCESSING_COMPLETE',
        data: result,
        success: true
      });
    } catch (error) {
      this.postMessage({
        type: 'PROCESSING_ERROR',
        error: error.message,
        success: false
      });
    } finally {
      this.isProcessing = false;
      this.currentTask = null;
    }
  }

  /**
   * Process data in chunks to prevent blocking
   */
  async chunkProcessData(data, options) {
    const chunkSize = options.chunkSize || 1000;
    const processed = {
      products: [],
      connections: [],
      categories: new Set(),
      metadata: {}
    };

    // Process products in chunks
    if (data.canvasProducts && Array.isArray(data.canvasProducts)) {
      const products = data.canvasProducts;
      const totalProducts = products.length;

      for (let i = 0; i < products.length; i += chunkSize) {
        const chunk = products.slice(i, i + chunkSize);
        const processedChunk = this.processProductChunk(chunk);
        
        processed.products.push(...processedChunk.products);
        processedChunk.categories.forEach(cat => processed.categories.add(cat));

        // Report progress
        const progress = Math.min(((i + chunkSize) / totalProducts) * 100, 100);
        this.postMessage({
          type: 'PROCESSING_PROGRESS',
          progress,
          processed: i + chunkSize,
          total: totalProducts,
          stage: 'products'
        });

        // Yield control
        await this.sleep(10);
      }
    }

    // Process connections in chunks
    if (data.connections && Array.isArray(data.connections)) {
      const connections = data.connections;
      const totalConnections = connections.length;

      for (let i = 0; i < connections.length; i += chunkSize) {
        const chunk = connections.slice(i, i + chunkSize);
        const processedChunk = this.processConnectionChunk(chunk);
        
        processed.connections.push(...processedChunk);

        // Report progress
        const progress = Math.min(((i + chunkSize) / totalConnections) * 100, 100);
        this.postMessage({
          type: 'PROCESSING_PROGRESS',
          progress,
          processed: i + chunkSize,
          total: totalConnections,
          stage: 'connections'
        });

        // Yield control
        await this.sleep(10);
      }
    }

    // Create indexes for fast searching
    processed.indexes = this.createIndexes(processed);
    processed.categories = Array.from(processed.categories);
    processed.metadata = {
      ...data.metadata,
      processedAt: new Date().toISOString(),
      productCount: processed.products.length,
      connectionCount: processed.connections.length,
      categoryCount: processed.categories.length
    };

    return processed;
  }

  /**
   * Process a chunk of products
   */
  processProductChunk(products) {
    const processed = {
      products: [],
      categories: new Set()
    };

    products.forEach(product => {
      // Validate and clean product data
      const cleanProduct = {
        id: product.id || this.generateId(),
        name: product.name || 'Unnamed Product',
        category: product.category || 'Uncategorized',
        subcategory: product.subcategory || '',
        price: this.parsePrice(product.price),
        description: product.description || '',
        image: product.image || '',
        position: product.position || { x: 0, y: 0 },
        metadata: product.metadata || {}
      };

      processed.products.push(cleanProduct);
      processed.categories.add(cleanProduct.category);
    });

    return processed;
  }

  /**
   * Process a chunk of connections
   */
  processConnectionChunk(connections) {
    return connections.map(connection => ({
      id: connection.id || this.generateId(),
      from: connection.from,
      to: connection.to,
      type: connection.type || 'default',
      metadata: connection.metadata || {}
    })).filter(conn => conn.from && conn.to);
  }

  /**
   * Create indexes for fast searching
   */
  createIndexes(data) {
    const indexes = {
      productById: new Map(),
      productsByCategory: new Map(),
      productsByName: new Map(),
      connectionsByProduct: new Map()
    };

    // Index products by ID
    data.products.forEach(product => {
      indexes.productById.set(product.id, product);
    });

    // Index products by category
    data.products.forEach(product => {
      if (!indexes.productsByCategory.has(product.category)) {
        indexes.productsByCategory.set(product.category, []);
      }
      indexes.productsByCategory.get(product.category).push(product);
    });

    // Index products by name (for searching)
    data.products.forEach(product => {
      const nameKey = product.name.toLowerCase();
      if (!indexes.productsByName.has(nameKey)) {
        indexes.productsByName.set(nameKey, []);
      }
      indexes.productsByName.get(nameKey).push(product);
    });

    // Index connections by product
    data.connections.forEach(connection => {
      // Index by 'from' product
      if (!indexes.connectionsByProduct.has(connection.from)) {
        indexes.connectionsByProduct.set(connection.from, []);
      }
      indexes.connectionsByProduct.get(connection.from).push(connection);

      // Index by 'to' product
      if (!indexes.connectionsByProduct.has(connection.to)) {
        indexes.connectionsByProduct.set(connection.to, []);
      }
      indexes.connectionsByProduct.get(connection.to).push(connection);
    });

    return indexes;
  }

  /**
   * Search products using indexes
   */
  searchProducts(query, indexes, options = {}) {
    const results = [];
    const limit = options.limit || 100;
    const queryLower = query.toLowerCase();

    // Search by name
    for (const [name, products] of indexes.productsByName) {
      if (name.includes(queryLower)) {
        results.push(...products);
        if (results.length >= limit) break;
      }
    }

    // Search by category if not enough results
    if (results.length < limit) {
      for (const [category, products] of indexes.productsByCategory) {
        if (category.toLowerCase().includes(queryLower)) {
          results.push(...products.slice(0, limit - results.length));
          if (results.length >= limit) break;
        }
      }
    }

    return results.slice(0, limit);
  }

  /**
   * Utility functions
   */
  generateId() {
    return 'id_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
  }

  parsePrice(price) {
    if (typeof price === 'number') return price;
    if (typeof price === 'string') {
      const parsed = parseFloat(price.replace(/[^0-9.-]/g, ''));
      return isNaN(parsed) ? 0 : parsed;
    }
    return 0;
  }

  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  postMessage(message) {
    if (typeof self !== 'undefined' && self.postMessage) {
      self.postMessage(message);
    }
  }
}

// Worker message handler
if (typeof self !== 'undefined') {
  const processor = new WorkerDataProcessor();

  self.onmessage = async function(e) {
    const { type, data, options } = e.data;

    switch (type) {
      case 'PROCESS_DATASET':
        await processor.processLargeDataset(data, options);
        break;

      case 'SEARCH_PRODUCTS':
        const results = processor.searchProducts(data.query, data.indexes, options);
        self.postMessage({
          type: 'SEARCH_RESULTS',
          data: results,
          query: data.query
        });
        break;

      case 'ABORT':
        processor.currentTask = null;
        processor.isProcessing = false;
        self.postMessage({
          type: 'ABORTED'
        });
        break;

      default:
        self.postMessage({
          type: 'ERROR',
          error: 'Unknown message type: ' + type
        });
    }
  };
}

// Export for use in main thread
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { WorkerDataProcessor };
}
