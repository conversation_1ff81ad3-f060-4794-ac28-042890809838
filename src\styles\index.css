@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    box-sizing: border-box;
  }

  body {
    margin: 0;
    font-family: 'Inter', system-ui, -apple-system, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    background-color: #0f172a;
    color: #f8fafc;
  }
}

@layer components {
  .sidebar-item {
    @apply flex items-center gap-3 p-3 rounded-lg cursor-pointer transition-all duration-200 hover:bg-dark-700 border border-transparent hover:border-dark-600;
  }

  .product-grid-item {
    @apply p-3 rounded-lg cursor-pointer transition-all duration-200 hover:bg-dark-700 border border-transparent hover:border-dark-600 w-full relative;
  }

  .product-grid-item:hover {
    @apply transform scale-105 shadow-lg;
  }

  .canvas-grid {
    background-image:
      linear-gradient(rgba(148, 163, 184, 0.1) 1px, transparent 1px),
      linear-gradient(90deg, rgba(148, 163, 184, 0.1) 1px, transparent 1px);
    background-size: 20px 20px;
  }

  .product-node {
    @apply cursor-move transition-all duration-200;
    background: transparent !important;
    border: none !important;
  }

  .connection-line {
    @apply stroke-primary-500 stroke-2 fill-none;
  }

  .dropdown-menu {
    @apply absolute;
    width: 350px; /* Fixed width for single box container */
    min-height: 320px; /* Minimum height for image + info layout */
    max-height: 500px; /* Allow expansion for longer product names */
    /* Zero gap positioning - no shadows or margins that create visual space */
    transition: opacity 0.2s ease-in-out, transform 0.2s ease-in-out;
    z-index: 9999; /* Ensure dropdown appears above all elements including buttons */
    pointer-events: auto; /* Ensure dropdown is interactive */
    opacity: 1;
    transform: scale(1);
  }

  .dropdown-menu.entering {
    opacity: 0;
    transform: scale(0.95);
  }

  /* Product hover preview styles - Bigger scale */
  .product-hover-preview {
    @apply fixed pointer-events-none transition-all duration-300;
    z-index: 9999 !important;
    animation: biggerScaleIn 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.175);
    filter: drop-shadow(0 20px 40px rgba(0, 0, 0, 0.4));
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(-50%) scale(0.95);
    }
    to {
      opacity: 1;
      transform: translateY(-50%) scale(1);
    }
  }

  @keyframes fadeInScale {
    from {
      opacity: 0;
      transform: translateY(-50%) scale(0.85);
    }
    to {
      opacity: 1;
      transform: translateY(-50%) scale(1);
    }
  }

  @keyframes biggerScaleIn {
    0% {
      opacity: 0;
      transform: translateY(-50%) scale(0.8);
    }
    50% {
      opacity: 0.9;
      transform: translateY(-50%) scale(1.02);
    }
    100% {
      opacity: 1;
      transform: translateY(-50%) scale(1);
    }
  }

  /* Ensure sidebar container has proper positioning */
  .sidebar-container {
    @apply relative;
  }

  /* Product image loading animation */
  .product-image-loading {
    @apply animate-pulse bg-gray-200;
  }

  /* Product image aspect ratio preservation */
  .product-image {
    @apply transition-all duration-200 ease-in-out;
    object-fit: contain !important;
    object-position: center !important;
    display: block !important;
    image-rendering: crisp-edges;
  }

  .product-image:hover {
    @apply transform scale-105;
  }

  /* Canvas product node image styling */
  .product-node img {
    object-fit: contain !important;
    object-position: center !important;
    display: block !important;
    max-width: 100% !important;
    max-height: 100% !important;
    width: auto !important;
    height: auto !important;
  }

  /* Sidebar product item image styling */
  .product-grid-item img {
    object-fit: contain !important;
    object-position: center !important;
    display: block !important;
  }

  /* Two-line text clamping for product names */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  /* Enhanced word breaking for product names */
  .break-words {
    word-wrap: break-word;
    overflow-wrap: break-word;
    word-break: break-word;
  }

  .hyphens-auto {
    hyphens: auto;
    -webkit-hyphens: auto;
    -moz-hyphens: auto;
  }

  /* Full product name display in dropdown - no line clamping */
  .product-name-full {
    white-space: normal;
    word-wrap: break-word;
    overflow-wrap: break-word;
    word-break: break-word;
    hyphens: auto;
    -webkit-hyphens: auto;
    -moz-hyphens: auto;
    text-align: center;
    max-width: 100%;
    /* Allow text to expand naturally without height restrictions */
  }

}

@layer utilities {
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: #475569 #1e293b;
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 8px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: #1e293b;
    border-radius: 4px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background: #475569;
    border-radius: 4px;
    border: 1px solid #1e293b;
    transition: background-color 0.2s ease;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: #64748b;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:active {
    background: #94a3b8;
  }

  /* Enhanced scroll behavior */
  .scroll-smooth {
    scroll-behavior: smooth;
  }

  /* Custom scrollbar for sidebar */
  .sidebar-scroll {
    scrollbar-width: thin;
    scrollbar-color: #64748b #1e293b;
    scroll-behavior: smooth;
  }

  .sidebar-scroll::-webkit-scrollbar {
    width: 12px;
  }

  .sidebar-scroll::-webkit-scrollbar-track {
    background: #1e293b;
    border-radius: 6px;
    margin: 2px;
  }

  .sidebar-scroll::-webkit-scrollbar-thumb {
    background: #475569;
    border-radius: 6px;
    border: 2px solid #1e293b;
    min-height: 20px;
    transition: all 0.2s ease;
  }

  .sidebar-scroll::-webkit-scrollbar-thumb:hover {
    background: #64748b;
    border-color: #334155;
  }

  .sidebar-scroll::-webkit-scrollbar-thumb:active {
    background: #94a3b8;
    border-color: #475569;
  }

  /* Enhanced scrollbar visibility */
  .sidebar-scroll::-webkit-scrollbar-thumb {
    opacity: 0.8;
  }

  .sidebar-scroll:hover::-webkit-scrollbar-thumb {
    opacity: 1;
  }

  /* Scrollbar corner */
  .sidebar-scroll::-webkit-scrollbar-corner {
    background: #1e293b;
  }

  /* Smooth drag and drop animations */
  .product-node {
    will-change: transform;
    backface-visibility: hidden;
    transform-style: preserve-3d;
  }

  /* Optimize dragging performance */
  .react-draggable-dragging {
    will-change: transform;
    pointer-events: none;
  }

  .react-draggable-dragging .product-node {
    transform: translateZ(0);
    backface-visibility: hidden;
  }

  /* Touch device optimizations */
  @media (hover: none) and (pointer: coarse) {
    .product-node {
      touch-action: none;
      -webkit-touch-callout: none;
      -webkit-user-select: none;
      user-select: none;
    }
  }

  /* Success message animation */
  .animate-fade-in {
    animation: fadeIn 0.3s ease-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
}
