import React from 'react';

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null, errorInfo: null };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI.
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    // You can also log the error to an error reporting service
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    this.setState({
      error: error,
      errorInfo: errorInfo
    });
  }

  render() {
    if (this.state.hasError) {
      // You can render any custom fallback UI
      return (
        <div className="min-h-screen bg-dark-900 text-white p-8">
          <div className="max-w-4xl mx-auto">
            <h1 className="text-4xl font-bold text-red-400 mb-4">Something went wrong!</h1>
            <div className="bg-dark-800 p-6 rounded-lg mb-6">
              <h2 className="text-xl font-semibold mb-4">Error Details:</h2>
              <div className="bg-red-900 bg-opacity-20 border border-red-500 p-4 rounded mb-4">
                <p className="text-red-300 font-mono text-sm">
                  {this.state.error && this.state.error.toString()}
                </p>
              </div>
              {this.state.errorInfo && (
                <details className="bg-dark-700 p-4 rounded">
                  <summary className="cursor-pointer text-yellow-400 mb-2">Stack Trace</summary>
                  <pre className="text-xs text-gray-300 overflow-auto">
                    {this.state.errorInfo.componentStack}
                  </pre>
                </details>
              )}
            </div>
            <button
              onClick={() => window.location.reload()}
              className="px-6 py-3 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors"
            >
              Reload Page
            </button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
