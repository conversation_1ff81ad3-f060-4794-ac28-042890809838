import React, { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { useDrag } from '../contexts/DragContext';

const ProductHoverPreview = ({
  product,
  position,
  isVisible,
  className = "",
  onImageError
}) => {
  const { isDraggingFromSidebar } = useDrag();
  const [imageError, setImageError] = useState(false);

  // Default placeholder image as SVG data URL
  const defaultPlaceholder = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA2MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjYwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjZjNmNGY2Ii8+CjxwYXRoIGQ9Ik0yNSAyMGwtNS01djEwaDEwdi0xMGwtNSA1eiIgZmlsbD0iIzlmYTZiNyIvPgo8L3N2Zz4=';

  // Determine the image source to use
  const getImageSrc = () => {
    // If there's no image at all, use the placeholder
    if (!product.image) {
      return defaultPlaceholder;
    }

    // If the image is a string, use it directly regardless of format
    if (typeof product.image === 'string') {
      // Check for http/https URLs
      if (product.image.startsWith('http')) {
        return product.image;
      }

      // Check for data URLs (base64)
      if (product.image.startsWith('data:')) {
        return product.image;
      }

      // For any other string format, try to use it
      return product.image;
    }

    // Fallback to placeholder for any other case
    return defaultPlaceholder;
  };

  const handleImageError = () => {
    setImageError(true);
    if (onImageError) {
      onImageError();
    }
  };

  if (!product || !isVisible || isDraggingFromSidebar()) {
    return null;
  }

  const previewContent = (
    <div
      className={`product-hover-preview fixed pointer-events-none transition-all duration-75 ease-out ${className}`}
      style={{
        left: `${position.x}px`,
        top: `${position.y}px`,
        transform: 'translateY(-50%)',
        zIndex: 99999
      }}
    >
      <div className="bg-dark-800 border-2 border-primary-500 rounded-xl shadow-2xl p-6 min-w-[450px] max-w-[500px]">
        {/* Extra Large Preview Image */}
        <div className="w-[420px] h-[315px] bg-white rounded-xl flex items-center justify-center overflow-hidden mb-4 shadow-lg border border-gray-200">
          {!imageError ? (
            <img
              src={getImageSrc()}
              alt={product.name}
              className="w-full h-full object-contain"
              onError={handleImageError}
            />
          ) : (
            <div className="w-full h-full bg-gray-100 rounded flex items-center justify-center text-gray-500">
              <div className="text-center">
                <div className="text-4xl mb-2">📦</div>
                <div className="text-sm">No Image</div>
              </div>
            </div>
          )}
        </div>

        {/* Product Details - Enhanced Size */}
        <div className="space-y-3">
          <h3 className="text-2xl font-bold text-white leading-tight line-clamp-2 overflow-hidden break-words hyphens-auto" style={{ lineHeight: '1.3', wordWrap: 'break-word', overflowWrap: 'break-word', hyphens: 'auto' }}>{product.name}</h3>
          <div className="flex items-center justify-between">
            <span className="text-lg text-dark-300 font-medium">{product.category}</span>
            <span className="text-3xl font-bold text-primary-400">{product.price}</span>
          </div>
          <div className="text-base text-dark-300 space-y-1">
            <div><span className="font-semibold text-white">Subcategory:</span> {product.subcategory}</div>
            <div><span className="font-semibold text-white">Size:</span> {product.diameter || product.size || 'N/A'}</div>
            <div><span className="font-semibold text-white">Material:</span> {product.material}</div>
          </div>
        </div>
      </div>
    </div>
  );

  // Use portal to render the preview at the document body level
  return createPortal(previewContent, document.body);
};

export default ProductHoverPreview;
