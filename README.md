# Plumber Design App

A comprehensive plumbing installation planning application with drag-and-drop functionality, built with React and featuring bilingual support (English/French).

## 🚀 Features

### ✅ **Core Functionality**
- **Drag & Drop Interface**: Products can be dragged from sidebar to canvas or clicked to add
- **YoroFlow-inspired Design**: Dark theme with modern UI matching professional workflow builders
- **Product Management**: Organized categories and subcategories with search functionality
- **Connection System**: Click "Connect Mode" to link products with curved SVG lines
- **Hover Details**: Rich product information dropdown on hover

### ✅ **Comprehensive Product Categories**
- **Appliance Supplies & Parts** (Dishwasher, Ice Maker, Washing Machine installations)
- **Bath Waste & Overflows** (Schedule 40 kits, Trim kits, Tubular kits)
- **Couplings & Gaskets** (Flexible couplings, No-Hub couplings)
- **Drain Openers** (Chemical, Bladders, Plungers, Snakes)
- **Drainage Products** (Floor drains, Roof drains, Shower drains, Cleanouts)
- **Fittings & Valves** (Brass, Copper, PVC, PEX, Ball valves, Check valves)
- **Gas Supplies & Fittings** (Connectors, PEXALGAS®, Burner kits)
- **Job Site Essentials** (Safety, Tools, Fasteners, Adhesives)
- **Kitchen & Bath** (Faucets, Sinks, Hardware, Accessories)
- **PEX Plumbing System** (PEX-A/B pipes, Fittings, Manifolds)
- **Pipe Hangers, Straps & Hooks** (Support systems, Insulators)
- **Pipe Insulation** (Poly, Rubber, Jackets, Sleeving)
- **Plumbing** (Repair parts, Water heaters, Access panels)
- **Repair & Testing Parts** (Gauges, Clamps, Test equipment)
- **Stub Outs** (Elbows, Straights, Valve connectors)
- **Toilet Seats** (Commercial, Residential)
- **Tools** (Wrenches, Cutters, Crimping tools, Measuring)
- **Tubular** (Fittings, Accessories, Components)

### ✅ **Product Management**
- **Search Bar**: Real-time filtering by name, category, material, etc.
- **Categorized Sidebar**: Collapsible categories with product counts
- **Upload Modal**: Add custom products with image upload or camera capture
- **Product Details**: Price, diameter, material information with hover tooltips

### ✅ **Canvas Features**
- **Grid Background**: Professional workflow appearance
- **Draggable Products**: Reposition products after placement with smooth animations
- **Connection Lines**: Smooth curved SVG lines between connected products
- **Selection System**: Visual feedback for selected products in connect mode
- **Delete Functionality**: Remove products with delete button or keyboard shortcuts

### ✅ **Export/Import**
- **Export as Image**: High-quality PNG export of the entire plan
- **Invoice Generation**: PDF invoice with itemized product list and total pricing
- **Plan Export/Import**: Save and load plans as JSON files for persistence
- **Undo/Redo**: Full history management for all canvas actions

### ✅ **Bilingual Support**
- **Language Toggle**: Switch between English and French with one click
- **Complete Translation**: All UI elements, categories, and messages translated
- **Persistent Settings**: Language preference saved to localStorage
- **Category Translation**: Product categories displayed in selected language

### ✅ **Advanced Settings System (NEW!)**
- **Settings Modal**: Comprehensive settings panel with organized tabs
- **Hierarchical Category Management**: Complete tree-view control over categories, subcategories, and individual products
- **Real-time Search**: Instant filtering across categories, subcategories, and products with text highlighting
- **Auto-expand Search Results**: Automatically expands matching categories/subcategories when searching
- **Bilingual Search**: Search works seamlessly in both English and French
- **Granular Product Control**: Remove/restore individual products with confirmation dialogs
- **Subcategory Management**: Remove/restore specific subcategories within categories
- **Category Management**: Toggle visibility and completely remove product categories
- **Progressive Confirmations**: Multi-level safety confirmations for all destructive actions
- **Permanent Deletion**: Permanently delete categories, subcategories, and products with strong warnings
- **Data Management**: Save/load projects, import/export functionality
- **Application Settings**: Language, grid visibility, auto-save preferences
- **Session Management**: Auto-save, exit confirmation, session persistence

### ✅ **Automatic Custom Product Integration (NEW!)**
- **Instant Sidebar Integration**: Custom products automatically appear in the correct category/subcategory
- **Real-time UI Updates**: Sidebar updates immediately without page refresh
- **Automatic Persistence**: Custom products saved to localStorage instantly upon creation
- **Settings Integration**: Custom products fully integrated with Category Management system
- **Consistent Behavior**: Custom products work identically to default products
- **Visual Success Feedback**: Clear confirmation when products are successfully added
- **Search Integration**: Custom products included in all search and filtering functionality

### ✅ **UI/UX**
- **Responsive Design**: Works on desktop and mobile devices
- **Professional Styling**: Dark theme with blue accents inspired by YoroFlow
- **Intuitive Controls**: Clear visual feedback and instructions
- **Mobile Support**: Camera capture for product photos
- **Accessibility**: Keyboard navigation and screen reader support

## 🎯 **How to Use**

### **Basic Operations**
1. **Language Selection**: Click the language toggle button (EN/FR) in the top toolbar
2. **Drag & Drop**: Try dragging products from the sidebar to the canvas
3. **Connect Mode**: Click the link button in toolbar, then click two products to connect them
4. **Product Details**: Hover over any product on the canvas to see detailed information
5. **Add Products**: Click "Add Product" to upload custom products with photos
6. **Export Options**: Use toolbar buttons to export as image, generate invoice, or save plan

### **Custom Product Integration**
7. **Automatic Sidebar Integration**: Custom products instantly appear in the correct category/subcategory
8. **Real-time Updates**: Sidebar updates immediately without requiring page refresh
9. **Automatic Persistence**: Custom products saved to localStorage instantly upon creation
10. **Visual Success Feedback**: Green success message confirms product was added successfully
11. **Settings Integration**: Custom products fully integrated with Category Management system
12. **Consistent Behavior**: Custom products work identically to default products for all operations

### **Settings & Configuration**
13. **Settings Panel**: Click the gear icon in the toolbar to open comprehensive settings
14. **Real-time Search**: Use the search bar to instantly find products, categories, or subcategories
15. **Auto-expand Results**: Search automatically expands matching categories to show relevant items
16. **Text Highlighting**: Search terms are highlighted in yellow for easy identification
17. **Hierarchical Management**: Expandable tree view showing Categories → Subcategories → Products
18. **Category Control**: Toggle visibility, remove, or permanently delete entire categories
19. **Subcategory Control**: Remove/restore specific subcategories (e.g., "Copper Fittings" within "Pipes")
20. **Individual Product Control**: Remove/restore specific products (e.g., specific pipe sizes)
21. **Progressive Confirmations**: Multi-level safety dialogs prevent accidental deletions
22. **Permanent Deletion**: Irreversible deletion with strong warning confirmations
23. **Project Management**: Save current work, load previous projects, or import/export data
24. **Auto-save**: Configure automatic saving intervals and exit behavior
25. **Grid Toggle**: Show/hide the canvas grid for precise alignment

## 🔧 **Technical Implementation**

- **React 18** with modern hooks and functional components
- **Vite** for fast development and building
- **Tailwind CSS** for responsive styling
- **React Draggable** for smooth drag interactions
- **HTML5 Canvas API** for export functionality
- **jsPDF** for invoice generation
- **html2canvas** for image export
- **Lucide React** for consistent icons
- **Context API** for language management

## 🌐 **Language Support**

The app supports:
- **English** (Default)
- **French** (Français)

All text, categories, and user interface elements are fully translated. The language preference is automatically saved and restored on subsequent visits.

## 🚀 **Getting Started**

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build
```

The app will be available at `http://localhost:3000`

## 📱 **Mobile Features**

- Touch-based drag and drop
- Camera integration for product photos
- Responsive sidebar and canvas
- Mobile-optimized touch interactions

## 🎨 **Design Inspiration**

The interface is inspired by YoroFlow's professional workflow builder, featuring:
- Dark theme with modern aesthetics
- Intuitive drag-and-drop interactions
- Professional grid-based canvas
- Clean, organized sidebar navigation

---

**Built with ❤️ for plumbing professionals and contractors**
