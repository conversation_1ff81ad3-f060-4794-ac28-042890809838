import React, { useState } from 'react';
import {
  Undo2,
  Redo2,
  Trash2,
  Link,
  FileImage,
  Receipt,
  Play,
  Languages,
  Database
} from 'lucide-react';
import {
  exportAsImage,
  exportAsInvoice,
  exportEnhancedReport,
  exportSelectedArea,
  getAggregatedInvoiceData
} from '../utils/exportUtils';
import { useLanguage } from '../contexts/LanguageContext';
import InvoicePreviewModal from './InvoicePreviewModal';

const Toolbar = ({
  onUndo,
  onRedo,
  onClear,
  onToggleConnect,
  isConnectMode,
  canUndo,
  canRedo,
  canvasProducts,
  connections,
  canvasRef,
  onOpenSettings,
  isSelectionMode,
  onToggleSelectionMode,
  onSelectionConfirm,
  onSelectionCancel,
  onSelectionClear
}) => {
  const { language, toggleLanguage, t } = useLanguage();
  const [showInvoicePreview, setShowInvoicePreview] = useState(false);
  const [invoiceData, setInvoiceData] = useState(null);

  const handleExportImage = async () => {
    if (isSelectionMode) {
      // If already in selection mode, cancel it and export normally
      onSelectionCancel();
      if (canvasRef.current) {
        await exportAsImage(canvasRef.current);
      }
    } else {
      // Enter selection mode
      onToggleSelectionMode();
    }
  };

  const handleSelectionExport = async (selectionRect) => {
    if (canvasRef.current && selectionRect) {
      await exportSelectedArea(canvasRef.current, selectionRect, canvasProducts);
      onSelectionConfirm(selectionRect);
    }
  };

  // Enhanced report preview handler (removed pricing references)
  const handleInvoicePreview = () => {
    if (canvasProducts.length === 0) {
      alert('No products on canvas to generate report.');
      return;
    }

    const result = getAggregatedInvoiceData(canvasProducts);
    if (result.success) {
      setInvoiceData(result.data);
      setShowInvoicePreview(true);
    } else {
      alert(`Failed to generate report preview: ${result.error}`);
    }
  };

  // Note: Direct PDF export removed - now only accessible through preview modal

  // Enhanced report preview modal handlers with pricing support
  const handleGeneratePDFFromPreview = (updatedInvoiceData) => {
    // If updated data is provided from the modal, use the enhanced export with pricing
    if (updatedInvoiceData) {
      // Use the enhanced export function that supports custom titles and pricing
      exportEnhancedReport(updatedInvoiceData);
    } else {
      // Fallback to basic export without pricing
      exportAsInvoice(canvasProducts);
    }
    setShowInvoicePreview(false);
  };



  const handleCloseInvoicePreview = () => {
    setShowInvoicePreview(false);
    setInvoiceData(null);
  };



  return (
    <div className="bg-dark-800 border-b border-dark-700">
      <div className="px-6 py-3 flex items-center justify-between">
        <div className="flex items-center gap-2">
          <div className="flex items-center gap-1 mr-4">
            <Play className="w-5 h-5 text-primary-500" />
            <span className="text-lg font-semibold text-white">{t('appTitle')}</span>
          </div>

          <button
            onClick={onUndo}
            disabled={!canUndo}
            className="p-2 rounded-lg bg-dark-700 hover:bg-dark-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            title={t('undo')}
          >
            <Undo2 className="w-4 h-4" />
          </button>

          <button
            onClick={onRedo}
            disabled={!canRedo}
            className="p-2 rounded-lg bg-dark-700 hover:bg-dark-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            title={t('redo')}
          >
            <Redo2 className="w-4 h-4" />
          </button>

          <div className="w-px h-6 bg-dark-600 mx-2" />

          <button
            onClick={onToggleConnect}
            className={`p-2 rounded-lg transition-colors ${
              isConnectMode
                ? 'bg-primary-600 hover:bg-primary-700 text-white'
                : 'bg-dark-700 hover:bg-dark-600'
            }`}
            title={t('connectMode')}
          >
            <Link className="w-4 h-4" />
          </button>

          <button
            onClick={onClear}
            className="p-2 rounded-lg bg-red-600 hover:bg-red-700 transition-colors"
            title={t('clearCanvas')}
          >
            <Trash2 className="w-4 h-4" />
          </button>
        </div>

        <div className="flex items-center gap-2">
        <button
          onClick={handleExportImage}
          className={`flex items-center gap-2 px-3 py-2 rounded-lg transition-colors ${
            isSelectionMode
              ? 'bg-primary-600 hover:bg-primary-700 text-white'
              : 'bg-dark-700 hover:bg-dark-600'
          }`}
          title={isSelectionMode ? t('Cancel Selection') || 'Cancel Selection' : t('exportImage')}
        >
          <FileImage className="w-4 h-4" />
          <span className="text-sm">
            {isSelectionMode ? t('Cancel Selection') || 'Cancel Selection' : t('exportImage')}
          </span>
        </button>

        <button
          onClick={handleInvoicePreview}
          className="flex items-center gap-2 px-3 py-2 rounded-lg bg-green-600 hover:bg-green-700 transition-colors"
          title="Preview Aggregated Report"
        >
          <Receipt className="w-4 h-4" />
          <span className="text-sm">Report Preview</span>
        </button>

        <div className="w-px h-6 bg-dark-600 mx-2" />

        {/* Settings Button */}
        <button
          onClick={onOpenSettings}
          className="flex items-center gap-2 px-3 py-2 rounded-lg bg-dark-700 hover:bg-dark-600 transition-colors"
          title={t('settings') || 'Settings'}
        >
          <Database className="w-4 h-4" />
          <span className="text-sm">{t('settings') || 'Settings'}</span>
        </button>

        {/* Language Toggle */}
        <button
          onClick={toggleLanguage}
          className="flex items-center gap-2 px-3 py-2 rounded-lg bg-dark-700 hover:bg-dark-600 transition-colors"
          title={t('language')}
        >
          <Languages className="w-4 h-4" />
          <span className="text-sm">{language === 'en' ? 'FR' : 'EN'}</span>
        </button>
        </div>
      </div>

      {/* Report Preview Modal */}
      <InvoicePreviewModal
        isOpen={showInvoicePreview}
        onClose={handleCloseInvoicePreview}
        invoiceData={invoiceData}
        onGeneratePDF={handleGeneratePDFFromPreview}
      />
    </div>
  );
};

export default Toolbar;
