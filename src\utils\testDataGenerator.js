/**
 * Test data generator for performance testing virtual scrolling
 * Generates large numbers of products for testing purposes
 */

// Sample product categories and subcategories
const testCategories = [
  {
    name: 'Pipes & Fittings',
    subcategories: ['Copper Pipes', 'PVC Pipes', 'Steel Pipes', 'Flexible Pipes', 'Pipe Fittings', 'Elbows', 'Tees', 'Reducers']
  },
  {
    name: 'Valves & Controls',
    subcategories: ['Ball Valves', 'Gate Valves', 'Check Valves', 'Pressure Relief', 'Flow Control', 'Shut-off Valves', 'Butterfly Valves']
  },
  {
    name: 'Fixtures & Appliances',
    subcategories: ['Sinks', 'Toilets', 'Showers', 'Bathtubs', 'Faucets', 'Water Heaters', 'Pumps', 'Filters']
  },
  {
    name: 'Tools & Equipment',
    subcategories: ['Pipe Cutters', 'Wrenches', 'Soldering Tools', 'Testing Equipment', 'Safety Gear', 'Measuring Tools']
  },
  {
    name: 'Drainage & Waste',
    subcategories: ['Drain Pipes', 'Waste Fittings', 'Traps', 'Cleanouts', 'Vents', 'Grease Traps', 'Floor Drains']
  },
  {
    name: 'Water Supply',
    subcategories: ['Supply Lines', 'Meters', 'Backflow Prevention', 'Pressure Tanks', 'Well Equipment', 'Distribution']
  },
  {
    name: 'Insulation & Protection',
    subcategories: ['Pipe Insulation', 'Pipe Hangers', 'Protective Sleeves', 'Tape & Sealants', 'Corrosion Protection']
  },
  {
    name: 'Gas & Fuel Systems',
    subcategories: ['Gas Pipes', 'Gas Valves', 'Regulators', 'Meters', 'Safety Equipment', 'Fuel Lines']
  }
];

// Sample materials
const materials = ['Copper', 'PVC', 'Steel', 'Brass', 'Cast Iron', 'Stainless Steel', 'Plastic', 'Aluminum', 'Galvanized Steel'];

// Sample sizes/diameters
const sizes = ['1/4"', '3/8"', '1/2"', '3/4"', '1"', '1-1/4"', '1-1/2"', '2"', '2-1/2"', '3"', '4"', '6"', '8"', '10"', '12"'];

// Sample brands
const brands = ['AquaFlow', 'PipeMaster', 'FlowTech', 'HydroMax', 'PlumbPro', 'WaterWorks', 'FluidDynamics', 'PressurePlus'];

// Sample product name templates
const productNameTemplates = [
  '{material} {type} {size}',
  '{brand} {type} - {size}',
  '{size} {material} {type}',
  'Professional {type} ({material})',
  '{brand} {material} {type} {size}',
  'Heavy Duty {type} - {material}',
  'Commercial {type} {size}',
  '{material} {type} Fitting {size}'
];

// Product type variations by category
const productTypes = {
  'Pipes & Fittings': ['Pipe', 'Elbow', 'Tee', 'Coupling', 'Reducer', 'Cap', 'Plug', 'Union', 'Nipple', 'Bushing'],
  'Valves & Controls': ['Valve', 'Control', 'Regulator', 'Actuator', 'Handle', 'Stem', 'Seat', 'Disc'],
  'Fixtures & Appliances': ['Fixture', 'Appliance', 'Unit', 'Assembly', 'Component', 'System'],
  'Tools & Equipment': ['Tool', 'Cutter', 'Wrench', 'Kit', 'Set', 'Equipment', 'Device'],
  'Drainage & Waste': ['Drain', 'Trap', 'Fitting', 'Pipe', 'Assembly', 'Cover', 'Grate'],
  'Water Supply': ['Line', 'Meter', 'Tank', 'Pump', 'Filter', 'System', 'Assembly'],
  'Insulation & Protection': ['Insulation', 'Hanger', 'Clamp', 'Sleeve', 'Tape', 'Sealant', 'Coating'],
  'Gas & Fuel Systems': ['Line', 'Valve', 'Regulator', 'Meter', 'Fitting', 'Connector', 'Safety Device']
};

/**
 * Generate a random product name
 * @param {string} category - Product category
 * @param {string} subcategory - Product subcategory
 * @returns {string} Generated product name
 */
const generateProductName = (category, subcategory) => {
  const template = productNameTemplates[Math.floor(Math.random() * productNameTemplates.length)];
  const types = productTypes[category] || ['Component'];
  const type = types[Math.floor(Math.random() * types.length)];
  const material = materials[Math.floor(Math.random() * materials.length)];
  const size = sizes[Math.floor(Math.random() * sizes.length)];
  const brand = brands[Math.floor(Math.random() * brands.length)];
  
  return template
    .replace('{material}', material)
    .replace('{type}', type)
    .replace('{size}', size)
    .replace('{brand}', brand);
};

/**
 * Generate a random price
 * @returns {number} Random price between $5 and $500
 */
const generatePrice = () => {
  return Math.round((Math.random() * 495 + 5) * 100) / 100;
};

/**
 * Generate a placeholder image (SVG data URL)
 * @returns {string} SVG data URL for placeholder image
 */
const generatePlaceholderImage = () => {
  const colors = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4', '#84CC16', '#F97316'];
  const color = colors[Math.floor(Math.random() * colors.length)];
  
  const svg = `<svg width="60" height="40" viewBox="0 0 60 40" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect width="60" height="40" fill="${color}"/>
    <circle cx="30" cy="20" r="8" fill="white" opacity="0.8"/>
  </svg>`;
  
  return `data:image/svg+xml;base64,${btoa(svg)}`;
};

/**
 * Generate test products for performance testing
 * @param {number} count - Number of products to generate
 * @returns {Array} Array of generated products
 */
export const generateTestProducts = (count = 1000) => {
  const products = [];
  
  for (let i = 0; i < count; i++) {
    const category = testCategories[Math.floor(Math.random() * testCategories.length)];
    const subcategory = category.subcategories[Math.floor(Math.random() * category.subcategories.length)];
    const material = materials[Math.floor(Math.random() * materials.length)];
    const size = sizes[Math.floor(Math.random() * sizes.length)];
    
    const product = {
      id: `test-product-${i + 1}`,
      name: generateProductName(category.name, subcategory),
      category: category.name,
      subcategory: subcategory,
      price: generatePrice(),
      material: material,
      diameter: size,
      size: size,
      image: generatePlaceholderImage(),
      isTestData: true,
      createdAt: new Date().toISOString()
    };
    
    products.push(product);
  }
  
  return products;
};

/**
 * Generate test categories for the category system
 * @returns {Array} Array of category objects
 */
export const generateTestCategories = () => {
  return testCategories.map(category => ({
    name: category.name,
    subcategories: category.subcategories.map(sub => ({
      name: sub,
      productCount: 0
    }))
  }));
};

/**
 * Performance testing utility
 * @param {Function} testFunction - Function to test
 * @param {string} testName - Name of the test
 * @returns {Object} Performance results
 */
export const performanceTest = (testFunction, testName = 'Test') => {
  const startTime = performance.now();
  const startMemory = performance.memory ? performance.memory.usedJSHeapSize : 0;
  
  const result = testFunction();
  
  const endTime = performance.now();
  const endMemory = performance.memory ? performance.memory.usedJSHeapSize : 0;
  
  const duration = endTime - startTime;
  const memoryUsed = endMemory - startMemory;
  
  console.log(`Performance Test: ${testName}`);
  console.log(`Duration: ${duration.toFixed(2)}ms`);
  if (performance.memory) {
    console.log(`Memory Used: ${(memoryUsed / 1024 / 1024).toFixed(2)}MB`);
  }
  
  return {
    duration,
    memoryUsed,
    result
  };
};

/**
 * Clear test data from products array
 * @param {Array} products - Products array
 * @returns {Array} Products array without test data
 */
export const clearTestData = (products) => {
  return products.filter(product => !product.isTestData);
};
