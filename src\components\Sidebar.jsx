import React, { useState, useMemo, useEffect, useRef } from 'react';
import { Search, Plus } from 'lucide-react';
import VirtualizedProductGrid from './VirtualizedProductGrid';
import NonVirtualProductGrid from './NonVirtualProductGrid';
import { productCategories } from '../data/products';
import { useLanguage } from '../contexts/LanguageContext';
import { useSettings } from '../contexts/SettingsContext';
import { searchProducts } from '../utils/searchUtils';
import { flattenProductHierarchy, calculateFlatListHeight } from '../utils/virtualScrollUtils';

const Sidebar = ({
  products,
  searchTerm,
  onSearchChange,
  onProductDragStart,
  onUploadClick
}) => {
  const { t, tc } = useLanguage();
  const { settings } = useSettings();
  const [expandedCategories, setExpandedCategories] = useState({});
  const scrollableAreaRef = useRef(null);
  const [containerHeight, setContainerHeight] = useState(0);
  const [useVirtualScrolling, setUseVirtualScrolling] = useState(false);

  // Auto-expand categories when searching
  const shouldAutoExpand = useMemo(() => {
    return searchTerm && searchTerm.trim().length > 0;
  }, [searchTerm]);

  // Filter products based on search term and category visibility
  const filteredProducts = useMemo(() => {
    let filtered = products;

    // Filter by search term using enhanced search utility
    if (searchTerm && searchTerm.trim()) {
      filtered = searchProducts(filtered, searchTerm);
    }

    // Filter by category visibility, removed categories, and deleted categories
    filtered = filtered.filter(product =>
      settings.visibleCategories[product.category] !== false &&
      !(settings.removedCategories || []).includes(product.category) &&
      !(settings.deletedCategories || []).includes(product.category)
    );

    // Filter by removed/deleted subcategories
    filtered = filtered.filter(product => {
      const isSubcategoryRemoved = (settings.removedSubcategories || []).some(
        item => item.category === product.category && item.subcategory === product.subcategory
      );
      const isSubcategoryDeleted = (settings.deletedSubcategories || []).some(
        item => item.category === product.category && item.subcategory === product.subcategory
      );
      return !isSubcategoryRemoved && !isSubcategoryDeleted;
    });

    // Filter by removed/deleted products
    filtered = filtered.filter(product =>
      !(settings.removedProducts || []).includes(product.id) &&
      !(settings.deletedProducts || []).includes(product.id)
    );

    return filtered;
  }, [products, searchTerm, settings.visibleCategories, settings.removedCategories, settings.deletedCategories, settings.removedSubcategories, settings.deletedSubcategories, settings.removedProducts, settings.deletedProducts]);

  // Group products by category and subcategory
  const groupedProducts = useMemo(() => {
    const grouped = {};

    filteredProducts.forEach(product => {
      if (!grouped[product.category]) {
        grouped[product.category] = {};
      }
      if (!grouped[product.category][product.subcategory]) {
        grouped[product.category][product.subcategory] = [];
      }
      grouped[product.category][product.subcategory].push(product);
    });

    return grouped;
  }, [filteredProducts]);

  // Measure container height and determine if virtual scrolling should be used
  useEffect(() => {
    const measureContainer = () => {
      if (scrollableAreaRef.current) {
        const height = scrollableAreaRef.current.clientHeight;
        setContainerHeight(height);
      }
    };

    measureContainer();

    // Set up resize observer to track container size changes
    const resizeObserver = new ResizeObserver(measureContainer);
    if (scrollableAreaRef.current) {
      resizeObserver.observe(scrollableAreaRef.current);
    }

    return () => resizeObserver.disconnect();
  }, []);

  // Automatically determine if virtual scrolling should be enabled
  useEffect(() => {
    if (containerHeight > 0 && Object.keys(groupedProducts).length > 0) {
      // Flatten the product hierarchy to calculate total content height
      const flatItems = flattenProductHierarchy(groupedProducts, expandedCategories, shouldAutoExpand);
      const totalContentHeight = calculateFlatListHeight(flatItems);

      // Enable virtual scrolling if content height exceeds container height by a threshold
      // This provides a buffer to avoid unnecessary switching for borderline cases
      const threshold = containerHeight * 1.5; // 50% buffer
      const shouldUseVirtual = totalContentHeight > threshold;

      setUseVirtualScrolling(shouldUseVirtual);
    }
  }, [containerHeight, groupedProducts, expandedCategories, shouldAutoExpand]);

  // Auto-expand categories and subcategories when products are available
  useEffect(() => {
    if (Object.keys(groupedProducts).length > 0 && Object.keys(expandedCategories).length === 0) {
      const expanded = {};

      // Expand all categories and their first subcategory to show more content
      Object.keys(groupedProducts).forEach(category => {
        expanded[category] = true;
        const firstSubcategory = Object.keys(groupedProducts[category])[0];
        if (firstSubcategory) {
          expanded[`${category}-${firstSubcategory}`] = true;
        }
      });

      setExpandedCategories(expanded);
    }
  }, [groupedProducts, expandedCategories]);

  // Clean up expanded categories when categories are deleted
  useEffect(() => {
    const availableCategories = Object.keys(groupedProducts);
    const currentExpandedKeys = Object.keys(expandedCategories);

    // Check if any expanded categories no longer exist
    const hasDeletedCategories = currentExpandedKeys.some(key => {
      if (key.includes('-')) {
        // This is a subcategory key (category-subcategory)
        const [category] = key.split('-');
        return !availableCategories.includes(category);
      } else {
        // This is a category key
        return !availableCategories.includes(key);
      }
    });

    if (hasDeletedCategories) {
      // Filter out expanded states for deleted categories
      const cleanedExpanded = Object.fromEntries(
        Object.entries(expandedCategories).filter(([key]) => {
          if (key.includes('-')) {
            const [category] = key.split('-');
            return availableCategories.includes(category);
          } else {
            return availableCategories.includes(key);
          }
        })
      );
      setExpandedCategories(cleanedExpanded);
    }
  }, [groupedProducts]);

  const toggleCategory = (category) => {
    setExpandedCategories(prev => ({
      ...prev,
      [category]: !prev[category]
    }));
  };

  const toggleSubcategory = (category, subcategory) => {
    const key = `${category}-${subcategory}`;
    setExpandedCategories(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  return (
    <div className="w-80 bg-dark-800 border-r border-dark-700 flex flex-col sidebar-container h-full max-h-screen">
      {/* Fixed Header */}
      <div className="flex-shrink-0 p-4 border-b border-dark-700 bg-dark-800 z-10">
        <h2 className="text-lg font-semibold text-white mb-3">{t('components')}</h2>

        {/* Search Bar */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-dark-400" />
          <input
            type="text"
            placeholder={t('searchComponents')}
            value={searchTerm}
            onChange={(e) => onSearchChange(e.target.value)}
            className="w-full pl-10 pr-4 py-2 bg-dark-700 border border-dark-600 rounded-lg text-white placeholder-dark-400 focus:outline-none focus:border-primary-500 transition-colors"
          />
        </div>

        {/* Upload Button */}
        <button
          onClick={onUploadClick}
          className="w-full mt-3 flex items-center justify-center gap-2 px-4 py-2 bg-primary-600 hover:bg-primary-700 rounded-lg transition-colors"
        >
          <Plus className="w-4 h-4" />
          <span className="text-sm font-medium">{t('addProduct')}</span>
        </button>
      </div>

      {/* Scrollable Content Area */}
      <div ref={scrollableAreaRef} className="flex-1 min-h-0">
        {/* Product Categories - Virtual or Non-Virtual */}
        {useVirtualScrolling ? (
          <VirtualizedProductGrid
            groupedProducts={groupedProducts}
            expandedCategories={expandedCategories}
            shouldAutoExpand={shouldAutoExpand}
            searchTerm={searchTerm}
            onToggleCategory={toggleCategory}
            onToggleSubcategory={toggleSubcategory}
            onProductDragStart={onProductDragStart}
          />
        ) : (
          <NonVirtualProductGrid
            groupedProducts={groupedProducts}
            expandedCategories={expandedCategories}
            shouldAutoExpand={shouldAutoExpand}
            searchTerm={searchTerm}
            onToggleCategory={toggleCategory}
            onToggleSubcategory={toggleSubcategory}
            onProductDragStart={onProductDragStart}
          />
        )}
      </div>
    </div>
  );
};

export default Sidebar;
