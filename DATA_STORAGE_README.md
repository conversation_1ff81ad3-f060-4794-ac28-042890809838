# PlomDesign Data Storage System

## Overview
The PlomDesign data storage system provides automatic data persistence, loading, and backup functionality using browser localStorage with a comprehensive folder-like structure for organization.

## Virtual Folder Structure
While using localStorage (browser-based), the system organizes data as if it were in the following folder structure:

```
PlomDesign/
├── data/
│   ├── exports/          # Exported files (downloadable JSON)
│   ├── imports/          # Imported configurations
│   └── backups/          # Automatic timestamped backups
├── canvas_state/         # Current canvas layout and products
├── connections/          # Product connections and relationships
├── user_preferences/     # User settings and preferences
└── settings/            # Application settings
```

## Storage Keys
- `plom_canvas_state` - Canvas products, connections, selections
- `plom_products` - Custom product definitions
- `plom_user_preferences` - Language, theme, sidebar settings
- `plom_connections` - Product connections data
- `plom_settings` - Auto-save, auto-load, backup settings
- `plom_backup_[timestamp]` - Timestamped backup files
- `plom_last_save_timestamp` - Last save time tracking

## Features

### 🔄 Auto-Loading
- Automatically loads previous work when the app starts
- Shows loading indicator during data restoration
- Handles first-time users gracefully
- Displays success notification when data is restored

### 💾 Auto-Saving
- Automatically saves canvas changes every 2 seconds (debounced)
- Creates backups before overwriting existing data
- Maintains up to 10 recent backups (configurable)
- Shows save status and timestamps

### 📁 Data Management
- **Export**: Download canvas data as JSON files
- **Import**: Upload and restore from JSON files
- **Backups**: View and restore from automatic backups
- **Clear**: Remove all stored data with confirmation

### 🔧 Settings
- **Auto-save**: Enable/disable automatic saving
- **Auto-load**: Enable/disable automatic loading on startup
- **Backup Interval**: Configure backup frequency
- **Max Backups**: Set maximum number of backups to keep

## Usage

### Accessing Data Management
1. Click the "Data" button in the toolbar
2. Use the tabs to navigate between:
   - **Overview**: Storage info and quick actions
   - **Backups**: View and restore backups
   - **Settings**: Configure auto-save/load preferences

### Export/Import Workflow
1. **Export**: Click "Export Data" to download current workspace
2. **Import**: Click "Import Data" and select a JSON file
3. **Backup**: Automatic backups are created before major changes

### Data Structure
```json
{
  "canvasProducts": [...],
  "connections": [...],
  "selectedProducts": [...],
  "settings": {
    "showGrid": true,
    "autoSave": true,
    "autoLoad": true,
    "backupInterval": 300000,
    "maxBackups": 10
  },
  "userPreferences": {
    "language": "en",
    "theme": "dark",
    "sidebarWidth": 320
  },
  "metadata": {
    "version": "1.0.0",
    "created": "2024-01-01T00:00:00.000Z",
    "lastModified": "2024-01-01T00:00:00.000Z",
    "totalSessions": 1
  }
}
```

## Technical Implementation

### Components
- `DataStorageManager` - Core storage operations
- `DataContext` - React context for data operations
- `DataLoadingIndicator` - Loading screen during data restoration
- `DataNotificationManager` - Success/error notifications
- `DataManagementPanel` - UI for data operations

### Error Handling
- Validates data structure before loading
- Graceful fallback to default data on corruption
- User-friendly error messages
- Automatic cleanup of corrupted backups

### Performance
- Debounced auto-save to prevent excessive writes
- Throttled storage info updates
- Efficient backup rotation
- Minimal memory footprint

## Browser Compatibility
- **Chrome**: Full support
- **Firefox**: Full support
- **Safari**: Full support
- **Edge**: Full support
- **Mobile**: Touch-optimized interface

## Storage Limits
- **localStorage**: ~5-10MB per domain (browser dependent)
- **Backup Rotation**: Automatically removes old backups
- **Storage Monitor**: Real-time storage usage display

## Security & Privacy
- All data stored locally in browser
- No server communication for data storage
- User controls all data export/import
- Clear data option for privacy

## Troubleshooting

### Data Not Loading
1. Check browser localStorage support
2. Verify data structure integrity
3. Clear corrupted data if necessary
4. Check browser storage quota

### Auto-Save Not Working
1. Verify auto-save is enabled in settings
2. Check browser localStorage permissions
3. Monitor storage quota warnings
4. Review browser console for errors

### Import/Export Issues
1. Verify JSON file format
2. Check file size limits
3. Ensure proper file permissions
4. Validate data structure

## Development

### Adding New Data Types
1. Add storage key to `STORAGE_KEYS`
2. Update `DEFAULT_DATA` structure
3. Add validation in `validateDataStructure`
4. Update export/import logic

### Extending Backup System
1. Modify `createBackup` method
2. Update `cleanOldBackups` logic
3. Add new backup metadata fields
4. Update UI components

## Future Enhancements
- Cloud storage integration
- Real-time collaboration
- Advanced backup scheduling
- Data compression
- Encrypted storage options
