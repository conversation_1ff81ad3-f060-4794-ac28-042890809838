import React from 'react';
import { splitTextWithHighlights } from '../utils/searchUtils';

/**
 * Component that renders text with highlighted search matches
 * @param {string} text - The text to display
 * @param {string} searchTerm - The search term to highlight
 * @param {string} className - Additional CSS classes for the container
 * @param {string} highlightClassName - CSS classes for highlighted text
 */
const HighlightedText = ({ 
  text, 
  searchTerm, 
  className = '', 
  highlightClassName = 'bg-yellow-200 text-yellow-900 px-1 rounded' 
}) => {
  if (!text) {
    return null;
  }

  // If no search term, return plain text
  if (!searchTerm || !searchTerm.trim()) {
    return <span className={className}>{text}</span>;
  }

  // Split text into parts with highlight information
  const parts = splitTextWithHighlights(text, searchTerm);

  return (
    <span className={className}>
      {parts.map((part, index) => (
        part.isHighlight ? (
          <mark key={index} className={highlightClassName}>
            {part.text}
          </mark>
        ) : (
          <span key={index}>{part.text}</span>
        )
      ))}
    </span>
  );
};

export default HighlightedText;
