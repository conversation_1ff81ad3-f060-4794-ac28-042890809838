import html2canvas from 'html2canvas';

// Cache for rendered product PNGs to avoid re-rendering
const productImageCache = new Map();

/**
 * Creates a clean, isolated PNG image of a product element
 * @param {Object} product - Product object with image, name, etc.
 * @param {Object} options - Rendering options
 * @returns {Promise<string>} - Data URL of the PNG image
 */
export const renderProductAsPNG = async (product, options = {}) => {
  const {
    width = 100,
    height = 70,
    scale = 2,
    backgroundColor = 'transparent',
    quality = 1.0
  } = options;

  // Create cache key based on product and options
  const cacheKey = `${product.id}-${width}x${height}-${scale}-${backgroundColor}`;
  
  // Return cached version if available
  if (productImageCache.has(cacheKey)) {
    return productImageCache.get(cacheKey);
  }

  try {
    // Create a temporary container for rendering
    const tempContainer = document.createElement('div');
    tempContainer.style.position = 'absolute';
    tempContainer.style.left = '-9999px';
    tempContainer.style.top = '-9999px';
    tempContainer.style.width = `${width}px`;
    tempContainer.style.height = `${height}px`;
    tempContainer.style.backgroundColor = backgroundColor === 'transparent' ? 'rgba(0,0,0,0)' : backgroundColor;
    tempContainer.style.display = 'flex';
    tempContainer.style.alignItems = 'center';
    tempContainer.style.justifyContent = 'center';
    tempContainer.style.overflow = 'hidden';

    // Create the product image element with proper aspect ratio preservation
    const imgElement = document.createElement('img');
    imgElement.src = product.image;
    imgElement.alt = product.name;
    imgElement.style.maxWidth = '100%';
    imgElement.style.maxHeight = '100%';
    imgElement.style.width = 'auto';
    imgElement.style.height = 'auto';
    imgElement.style.objectFit = 'contain';
    imgElement.style.objectPosition = 'center';
    imgElement.style.display = 'block';

    // Create fallback element for when image fails to load
    const fallbackElement = document.createElement('div');
    fallbackElement.style.width = '100%';
    fallbackElement.style.height = '100%';
    fallbackElement.style.backgroundColor = 'transparent';
    fallbackElement.style.display = 'none';
    fallbackElement.style.alignItems = 'center';
    fallbackElement.style.justifyContent = 'center';
    fallbackElement.style.fontSize = '10px';
    fallbackElement.style.color = '#ffffff';
    fallbackElement.style.fontWeight = '500';
    fallbackElement.style.textAlign = 'center';
    fallbackElement.style.textShadow = '1px 1px 2px rgba(0,0,0,0.8)';
    fallbackElement.textContent = product.name.substring(0, 3).toUpperCase();

    tempContainer.appendChild(imgElement);
    tempContainer.appendChild(fallbackElement);
    document.body.appendChild(tempContainer);

    // Wait for image to load or handle error
    const imageLoadPromise = new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Image load timeout'));
      }, 5000);

      imgElement.onload = () => {
        clearTimeout(timeout);
        resolve();
      };

      imgElement.onerror = () => {
        clearTimeout(timeout);
        // Show fallback and continue
        imgElement.style.display = 'none';
        fallbackElement.style.display = 'flex';
        resolve();
      };

      // If image is already loaded (cached)
      if (imgElement.complete) {
        clearTimeout(timeout);
        resolve();
      }
    });

    await imageLoadPromise;

    // Render the container to canvas with transparent background
    const canvas = await html2canvas(tempContainer, {
      backgroundColor: null, // Transparent background
      scale: scale,
      useCORS: true,
      allowTaint: true,
      width: width,
      height: height,
      logging: false,
      removeContainer: false,
      ignoreElements: (element) => {
        // Ignore any elements that might have backgrounds
        return element.style && element.style.backgroundColor &&
               element.style.backgroundColor !== 'transparent' &&
               element.style.backgroundColor !== 'rgba(0, 0, 0, 0)';
      }
    });

    // Clean up temporary container
    document.body.removeChild(tempContainer);

    // Convert to PNG data URL
    const pngDataUrl = canvas.toDataURL('image/png', quality);

    // Cache the result
    productImageCache.set(cacheKey, pngDataUrl);

    return pngDataUrl;

  } catch (error) {
    console.error('Error rendering product as PNG:', error);
    
    // Return a fallback PNG for the product
    return createFallbackProductPNG(product, { width, height, scale });
  }
};

/**
 * Creates a fallback PNG image for products that fail to render
 * @param {Object} product - Product object
 * @param {Object} options - Rendering options
 * @returns {string} - Data URL of fallback PNG
 */
const createFallbackProductPNG = (product, options = {}) => {
  const { width = 100, height = 70, scale = 2 } = options;
  
  // Create a canvas for the fallback
  const canvas = document.createElement('canvas');
  canvas.width = width * scale;
  canvas.height = height * scale;
  const ctx = canvas.getContext('2d');

  // Set high DPI scaling
  ctx.scale(scale, scale);

  // Clear with transparent background
  ctx.clearRect(0, 0, width, height);

  // Draw text with shadow for visibility on dark background
  ctx.fillStyle = '#ffffff';
  ctx.font = 'bold 12px Arial, sans-serif';
  ctx.textAlign = 'center';
  ctx.textBaseline = 'middle';

  // Add text shadow for better visibility
  ctx.shadowColor = 'rgba(0, 0, 0, 0.8)';
  ctx.shadowBlur = 2;
  ctx.shadowOffsetX = 1;
  ctx.shadowOffsetY = 1;

  ctx.fillText(product.name.substring(0, 3).toUpperCase(), width / 2, height / 2);

  return canvas.toDataURL('image/png', 1.0);
};

/**
 * Preloads and caches PNG images for multiple products
 * @param {Array} products - Array of product objects
 * @param {Object} options - Rendering options
 * @returns {Promise<Map>} - Map of product IDs to PNG data URLs
 */
export const preloadProductPNGs = async (products, options = {}) => {
  const results = new Map();
  
  // Process products in batches to avoid overwhelming the browser
  const batchSize = 5;
  for (let i = 0; i < products.length; i += batchSize) {
    const batch = products.slice(i, i + batchSize);
    const batchPromises = batch.map(async (product) => {
      try {
        const pngDataUrl = await renderProductAsPNG(product, options);
        results.set(product.id, pngDataUrl);
      } catch (error) {
        console.error(`Failed to preload PNG for product ${product.id}:`, error);
        results.set(product.id, createFallbackProductPNG(product, options));
      }
    });
    
    await Promise.all(batchPromises);
  }
  
  return results;
};

/**
 * Clears the product image cache
 */
export const clearProductImageCache = () => {
  productImageCache.clear();
};

/**
 * Gets cached PNG for a product if available
 * @param {string} productId - Product ID
 * @param {Object} options - Rendering options for cache key
 * @returns {string|null} - Cached PNG data URL or null
 */
export const getCachedProductPNG = (productId, options = {}) => {
  const {
    width = 100,
    height = 70,
    scale = 2,
    backgroundColor = 'transparent'
  } = options;
  
  const cacheKey = `${productId}-${width}x${height}-${scale}-${backgroundColor}`;
  return productImageCache.get(cacheKey) || null;
};
